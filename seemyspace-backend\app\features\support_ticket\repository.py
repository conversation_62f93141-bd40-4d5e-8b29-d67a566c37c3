from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_
from sqlalchemy.orm import selectinload
from fastapi import status

from typing import Dict, Any, List, Optional

from app.features.support_ticket.schemas import CreateSupportTicket, SupportTicketFilterParams
from app.models.support_ticket import SupportTicket, TicketStatus
from app.models.support_ticket_category import SupportTicketCategory
from app.models.user import AccountType, User
from app.core.response import ResponseModal
from app.utils.constants import RESPONSE_MESSAGES
from app.core.logging_config import get_logger, setup_logging

import sentry_sdk
from datetime import datetime
from sqlalchemy.exc import IntegrityError


setup_logging(level="DEBUG")
logger = get_logger(__name__)


class SupportTicketRepository:
    """Repository for support ticket-related database operations."""

    def generate_ticket_id(self, ticket_id: int) -> str:
        """
        Generate a unique ticket ID in format: STXXXXXXXX

        Returns:
            str: Unique ticket ID
        """
        return f"ST{ticket_id:03d}"

    async def create_support_ticket(
        self,
        db: AsyncSession,
        ticket_data: CreateSupportTicket,
        user: Dict[str, Any]
    ) -> ResponseModal:
        """
        Create a new support ticket in the database.

        Args:
            db: Async database session
            ticket_data: Support ticket creation schema
            user_id: Optional user ID (from JWT token if authenticated)

        Returns:
            ResponseModal with success status and ticket data
        """
        try:
            # Verify that the category exists
            category_stmt = select(SupportTicketCategory).where(
                SupportTicketCategory.id == ticket_data.support_ticket_categories_id
            )
            category_result = await db.execute(category_stmt)
            category = category_result.scalar_one_or_none()

            if not category:
                return ResponseModal(
                    success=False,
                    message=RESPONSE_MESSAGES["CATEGORY_NOT_FOUND"],
                    status_code=status.HTTP_404_NOT_FOUND
                )

            # Create new support ticket with temporary ticket_id
            # We'll update it after getting the auto-generated ID
            new_ticket = SupportTicket(
                user_id=user["user_id"],
                support_ticket_categories_id=ticket_data.support_ticket_categories_id,
                subject=ticket_data.subject,
                message=ticket_data.message,
                images=ticket_data.images,
                status=TicketStatus.PENDING,
                ticket_id="TEMP"  
            )

            db.add(new_ticket)
            await db.flush()

            # Generate unique ticket ID based on database ID (e.g., ST001, ST002)
            ticket_id = self.generate_ticket_id(new_ticket.id)
            new_ticket.ticket_id = ticket_id
            await db.commit()

            await db.refresh(new_ticket)

            return ResponseModal(
                success=True,
                message=RESPONSE_MESSAGES["SUCCESS"],
                status_code=status.HTTP_201_CREATED
            )

        except Exception as e:
            logger.error(
                f"Unexpected error during support ticket creation: {e}")
            sentry_sdk.capture_exception(e)
            await db.rollback()
            return ResponseModal(
                success=False,
                message=RESPONSE_MESSAGES["WENT_WRONG"],
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def get_all_support_tickets(
        self,
        db: AsyncSession,
        filters: SupportTicketFilterParams
    ) -> ResponseModal:
        """
        Retrieve all support tickets from the database with pagination and filters.

        Args:
            db: Async database session
            filters: Filter and pagination parameters

        Returns:
            ResponseModal with list of support tickets
        """
        try:
            # Ensure valid pagination parameters
            page = max(0, filters.page)
            limit = max(1, filters.limit)
            offset = page * limit

            # Build base query with join to User table and eager load user and category relationships
            query = select(SupportTicket).options(
                selectinload(SupportTicket.user),
                selectinload(SupportTicket.category)
            ).join(
                User, SupportTicket.user_id == User.id, isouter=True)

            # Apply search filter (search by user name, ticket ID, or subject)
            if filters.search:
                search_pattern = f"%{filters.search.lower()}%"
                query = query.where(
                    or_(
                        func.lower(User.name).like(search_pattern),
                        func.lower(SupportTicket.ticket_id).like(
                            search_pattern),
                        func.lower(SupportTicket.subject).like(search_pattern)
                    )
                )

            # Apply user type filter (filter by account type of ticket creator)
            if filters.user_type:
                user_type_enum = AccountType(filters.user_type.lower())
                query = query.where(User.account_type == user_type_enum)

            # Apply status filter
            if filters.status:
                status_enum = TicketStatus(filters.status.lower())
                query = query.where(SupportTicket.status == status_enum)

            # Apply category filter
            if filters.category_id:
                query = query.where(
                    SupportTicket.support_ticket_categories_id == filters.category_id)

            # Apply date range filter
            if filters.start_date:
                query = query.where(
                    SupportTicket.created_at >= filters.start_date)

            if filters.end_date:
                # Include the entire end date (up to 23:59:59)
                end_date_with_time = filters.end_date.replace(
                    hour=23, minute=59, second=59, microsecond=999999)
                query = query.where(
                    SupportTicket.created_at <= end_date_with_time)

            # Apply ordering and pagination
            query = query.order_by(SupportTicket.created_at.desc(
            ), SupportTicket.id.desc()).limit(limit).offset(offset)

            # Execute query
            result = await db.execute(query)
            tickets = result.scalars().all()

            # Format results with user information
            tickets_data = [
                {
                    "id": ticket.id,
                    "ticket_id": ticket.ticket_id,
                    "category_id": ticket.support_ticket_categories_id,
                    "subject": ticket.subject,
                    "message": ticket.message,
                    "status": ticket.status.value,
                    "name": ticket.user.name,
                    "account_type": ticket.user.account_type.value,
                    "email": ticket.user.email,
                    "created_at": ticket.created_at,
                    "category_name": ticket.category.name,
                }
                for ticket in tickets
            ]

            return ResponseModal(
                success=True,
                message=RESPONSE_MESSAGES["SUCCESS"],
                data=tickets_data,
                status_code=status.HTTP_200_OK
            )

        except Exception as e:
            logger.error(f"Error fetching all support tickets: {e}")
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message=RESPONSE_MESSAGES["WENT_WRONG"],
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def get_support_ticket_by_id(
        self,
        db: AsyncSession,
        ticket_id: int
    ) -> ResponseModal:
        """
        Retrieve a specific support ticket by ID.

        Args:
            db: Async database session
            ticket_id: Support ticket ID to retrieve

        Returns:
            ResponseModal with ticket data or error message
        """
        try:
            stmt = select(SupportTicket).options(
                selectinload(SupportTicket.user),
                selectinload(SupportTicket.category)
            ).where(SupportTicket.id == ticket_id)
            result = await db.execute(stmt)
            ticket = result.scalar_one_or_none()

            if not ticket:
                return ResponseModal(
                    success=False,
                    message=RESPONSE_MESSAGES["TICKET_NOT_FOUND"],
                    status_code=status.HTTP_404_NOT_FOUND
                )

            ticket_data = {
                "id": ticket.id,
                "ticket_id": ticket.ticket_id,
                "category_id": ticket.support_ticket_categories_id,
                "subject": ticket.subject,
                "message": ticket.message,
                "status": ticket.status.value,
                "images": ticket.images,
                "additional_info": ticket.additional_info,
                "created_at": ticket.created_at,
                "user": {
                    "id": ticket.user.id,
                    "name": ticket.user.name,
                    "email": ticket.user.email,
                    "account_type": ticket.user.account_type.value
                } if ticket.user else None,
                "category": {
                    "id": ticket.category.id,
                    "name": ticket.category.name
                } if ticket.category else None
            }


            return ResponseModal(
                success=True,
                message=RESPONSE_MESSAGES["SUCCESS"],
                data=ticket_data,
                status_code=status.HTTP_200_OK
            )

        except Exception as e:
            logger.error(
                f"Unexpected error during support ticket retrieval: {e}")
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message=RESPONSE_MESSAGES["WENT_WRONG"],
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def update_ticket_status(
        self,
        db: AsyncSession,
        ticket_id: int,

    ) -> ResponseModal:
        """
        Update the status of a support ticket.

        Args:
            db: Async database session
            ticket_id: ID of the ticket to update

        Returns:
            ResponseModal with success status
        """
        try:
            # Get the ticket
            stmt = select(SupportTicket).where(SupportTicket.id == ticket_id)
            result = await db.execute(stmt)
            ticket = result.scalar_one_or_none()

            if not ticket:
                return ResponseModal(
                    success=False,
                    message=RESPONSE_MESSAGES["TICKET_NOT_FOUND"],
                    status_code=status.HTTP_404_NOT_FOUND
                )

            # Check if ticket is already resolved
            if ticket.status == TicketStatus.RESOLVED:
                return ResponseModal(
                    success=False,
                    message=RESPONSE_MESSAGES["TICKET_ALREADY_RESOLVED"],
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Update the status
            new_status = TicketStatus("resolved")
            ticket.status = new_status
            await db.commit()
            await db.refresh(ticket)

            return ResponseModal(
                success=True,
                message=RESPONSE_MESSAGES["SUCCESS"],
                status_code=status.HTTP_200_OK
            )

        except Exception as e:
            logger.error(f"Error updating ticket status: {e}")
            sentry_sdk.capture_exception(e)
            await db.rollback()
            return ResponseModal(
                success=False,
                message=RESPONSE_MESSAGES["WENT_WRONG"],
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def create_category(
        self,
        db: AsyncSession,
        name: str,
    ) -> ResponseModal:
        """
        Create a new support ticket category.

        Args:
            db (AsyncSession): Database session.
            name (str): Name of the category.

        Returns:
            ResponseModal: Response with success status and created category data or error message.
        """
        try:
            existing_category = await db.execute(
                select(SupportTicketCategory).where(
                    SupportTicketCategory.name == name,
                    SupportTicketCategory.is_active == True
                )
            )
            if existing_category.scalar_one_or_none():
                return ResponseModal(
                    success=False,
                    message="Category name already exists.",
                    status_code=status.HTTP_409_CONFLICT
                )

            new_category = SupportTicketCategory(
                name=name.strip(),
            )

            db.add(new_category)
            await db.commit()
            await db.refresh(new_category)

            return ResponseModal(
                success=True,
                message="Support ticket category created successfully.",
                status_code=status.HTTP_201_CREATED,
                data={
                    "id": new_category.id,
                    "name": new_category.name,
                    "created_at": new_category.created_at,
                    "updated_at": new_category.updated_at,
                }
            )

        except IntegrityError as e:
            await db.rollback()
            logger.error(f"[SupportCategory] Integrity error: {e}")
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message="Duplicate entry found.",
                status_code=status.HTTP_409_CONFLICT
            )

        except Exception as e:
            await db.rollback()
            logger.error(f"[SupportCategory] Unexpected error: {e}")
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message="Failed to create support ticket category.",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def list_categories(self, db: AsyncSession) -> ResponseModal:
        """
        List all active support ticket categories.

        Args:
            db: Async database session

        Returns:
            ResponseModal: Response with list of categories
        """
        try:
            # Query to get all active categories
            query = select(SupportTicketCategory).where(
                SupportTicketCategory.is_active == True
            ).order_by(SupportTicketCategory.name)
            
            result = await db.execute(query)
            categories = result.scalars().all()

            # Build response data
            category_list = []
            for cat in categories:
                category_list.append({
                    "id": cat.id,
                    "name": cat.name,
                    "is_active": cat.is_active,
                    "created_at": cat.created_at.isoformat(),
                    "updated_at": cat.updated_at.isoformat()
                })

            return ResponseModal(
                success=True,
                message="Categories retrieved successfully",
                data=category_list,
                status_code=status.HTTP_200_OK
            )

        except Exception as error:
            logger.error(f"Failed to list categories: {error}")
            sentry_sdk.capture_exception(error)
            return ResponseModal(
                success=False,
                message="Could not retrieve categories",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def get_category_by_id(self, db: AsyncSession, category_id: int) -> ResponseModal:
        """
        Retrieve a support ticket category by its ID.

        Args:
            db: Async database session
            category_id: ID of the category to retrieve

        Returns:
            ResponseModal: Response with category data or error message
        """
        try:
            stmt = select(SupportTicketCategory).where(
                SupportTicketCategory.id == category_id)
            result = await db.execute(stmt)
            category = result.scalar_one_or_none()

            if not category:
                return ResponseModal(
                    success=False,
                    message="Support ticket category not found.",
                    status_code=status.HTTP_404_NOT_FOUND
                )

            return ResponseModal(
                success=True,
                message="Support ticket category fetched successfully.",
                status_code=status.HTTP_200_OK,
                data={
                    "id": category.id,
                    "name": category.name,

                }
            )

        except Exception as e:
            logger.error(f"Error fetching category by id {category_id}: {e}")
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message="Failed to fetch support ticket category.",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def update_support_ticket_category(
        self,
        db: AsyncSession,
        category_id: int,
        name: str
    ) -> ResponseModal:
        """
        Update a support ticket category name.

        Args:
            db: Async database session
            category_id: ID of the support ticket category to update
            name: New name for the category

        Returns:
            ResponseModal with success or failure status
        """
        try:
            # Check if category exists
            category = await db.get(SupportTicketCategory, category_id)
            if not category:
                return ResponseModal(
                    success=False,
                    message="Support ticket category not found.",
                    status_code=status.HTTP_404_NOT_FOUND
                )

            # Check if new name already exists (case-insensitive, excluding current category)
            existing_category = await db.execute(
                select(SupportTicketCategory).where(
                    SupportTicketCategory.name.ilike(name.strip()),
                    SupportTicketCategory.id != category_id,
                    SupportTicketCategory.is_active == True
                )
            )
            if existing_category.scalar_one_or_none():
                return ResponseModal(
                    success=False,
                    message="Category name already exists.",
                    status_code=status.HTTP_409_CONFLICT
                )

            # Update category name
            category.name = name.strip()
            db.add(category)
            await db.commit()
            await db.refresh(category)

            return ResponseModal(
                success=True,
                message="Support ticket category updated successfully.",
                status_code=status.HTTP_200_OK,
                data={
                    "id": category.id,
                    "name": category.name,
                    "is_active": category.is_active,
                    "created_at": category.created_at.isoformat(),
                    "updated_at": category.updated_at.isoformat(),
                }
            )

        except IntegrityError as e:
            await db.rollback()
            logger.error(f"[SupportCategory] Integrity error during update: {e}")
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message="Duplicate entry found.",
                status_code=status.HTTP_409_CONFLICT
            )

        except Exception as e:
            await db.rollback()
            logger.error(f"[SupportCategory] Unexpected error during update: {e}")
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message="Something went wrong while updating the support ticket category.",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def delete_support_ticket_category(self, db: AsyncSession, category_id: int) -> ResponseModal:
        """
        Soft delete a support ticket category by marking it as inactive.

        Args:
            db: Async database session
            category_id: ID of the support ticket category to delete

        Returns:
            ResponseModal with success or failure status
        """
        try:

            category = await db.get(SupportTicketCategory, category_id)
            if not category:
                return ResponseModal(
                    success=False,
                    message="Support ticket category not found.",
                    status_code=status.HTTP_404_NOT_FOUND
                )

            category.is_active = False
            db.add(category)
            await db.commit()

            return ResponseModal(
                success=True,
                message="Support ticket category deleted successfully.",
                status_code=status.HTTP_200_OK
            )

        except Exception as e:
            await db.rollback()
            logger.error(
                f"Unexpected error during support ticket category deletion: {e}")
            return ResponseModal(
                success=False,
                message="Something went wrong while deleting the support ticket category.",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
