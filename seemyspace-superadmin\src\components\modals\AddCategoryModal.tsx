import React, { useEffect } from "react";
import { useForm, type UseFormSetError } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";

import Modal from "../common/Modal";
import Button from "../formElements/Button";
import Textbox from "../formElements/Textbox";
import InputWrapper from "../formElements/InputWrapper";
import { createCategoryValidationSchema } from "../../validationSchema/categoryValidation";
import type { ICategoryFormData } from "../../interfaces/categoryInterfaces";

interface AddCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (categoryName: string, setError: UseFormSetError<ICategoryFormData>) => Promise<void>;
  title?: string;
  mode?: "add" | "edit";
  initialData?: { id: number; name: string };
  isSubmitting?: boolean;
}

/**
 * Modal component for adding or editing support ticket categories
 * Supports both create and update modes with validation
 */
const AddCategoryModal: React.FC<AddCategoryModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  title = "Add New Support Ticket Category",
  mode = "add",
  initialData,
  isSubmitting = false,
}) => {
  const {
    control,
    handleSubmit,
    reset,
    setError,
    formState: { errors },
  } = useForm<ICategoryFormData>({
    resolver: yupResolver(createCategoryValidationSchema),
    defaultValues: {
      categoryName: "",
    },
    mode: "onChange",
  });

  /**
   * Update form when modal opens with initial data (for edit mode)
   */
  useEffect(() => {
    if (isOpen && mode === "edit" && initialData) {
      reset({
        categoryName: initialData.name,
      });
    } else if (isOpen && mode === "add") {
      reset({
        categoryName: "",
      });
    }
  }, [isOpen, mode, initialData, reset]);

  /**
   * Handle form submission
   */
  const handleFormSubmit = async (data: ICategoryFormData): Promise<void> => {
    await onSubmit(data.categoryName, setError);
    // If we reach here, submission was successful
    // Modal will close and form will reset
    reset();
    onClose();
  };

  /**
   * Handle cancel button click
   */
  const handleCancel = (): void => {
    reset();
    onClose();
  };

  const submitButtonText = mode === "edit" ? "Update Category" : "Add Category";

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title} size="md">
      <form onSubmit={handleSubmit(handleFormSubmit)} className="add-category-form">
        <InputWrapper>
          <InputWrapper.Label required>Category Name</InputWrapper.Label>
          <Textbox
            type="text"
            name="categoryName"
            placeholder="Enter category name"
            control={control}
            className="form-control"
            isDisabled={isSubmitting}
          />
          <InputWrapper.Error message={errors.categoryName?.message} />
        </InputWrapper>

        <div className="button-align gap-3">
          <Button
            type="submit"
            className="primary-btn rounded-md w-100"
            disabled={isSubmitting}
            loading={isSubmitting}
          >
            {submitButtonText}
          </Button>
          <Button
            type="button"
            className="dark-outline-btn rounded-md w-100"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddCategoryModal;
