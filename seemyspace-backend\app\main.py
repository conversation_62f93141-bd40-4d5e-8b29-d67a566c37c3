from contextlib import asynccontextmanager
import json
import time

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logging_config import setup_logging, get_logger
from app.core.redis_client import redis_client
from app.core.sentry import init_sentry
from app.features.common.auth.routes import router as auth_router
from app.features.admin.user_management.routes import router as user_management_router
from app.features.faqs.routes import router as faq_router
from app.features.furniture_store_details.routes import router as furniture_store_router
from app.features.product_details.routes import router as product_router
from app.features.support_ticket.routes import router as support_ticket_router
from app.utils import constants

# Initialize logging with DEBUG level to see all logs
setup_logging(level="DEBUG")
logger = get_logger(__name__)


# Request Logging Middleware
class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log all incoming requests with detailed information."""

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Extract request information
        method = request.method
        path = request.url.path
        query_params = dict(request.query_params)
        
        # Get request body for POST/PUT/PATCH/DELETE/GET requests
        body_data = None
        if method in ["POST", "PUT", "PATCH", "DELETE", "GET"]:
            try:
                body = await request.body()
                if body:
                    # Try to parse as JSON
                    try:
                        body_data = json.loads(body.decode())
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        body_data = f"<Binary data: {len(body)} bytes>"
            except Exception as e:
                body_data = f"<Error reading body: {str(e)}>"
        
        # Log the incoming request
        logger.info(f" INCOMING REQUEST: {method} {path}")
        
        if query_params:
            logger.info(f" Query Params: {query_params}")
            
        if body_data:
            logger.info(f" Request Body: {json.dumps(body_data, indent=2)}")
        
        # Process the request
        response = await call_next(request)
        
        # Calculate response time
        process_time = time.time() - start_time
        
        # Log the response
        logger.info(f" RESPONSE: {method} {path} -> {response.status_code} "
                   f"({process_time:.3f}s)")
        
        # Add response time header
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


# Security Headers Middleware
class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)

        for header, value in constants.SECURITY_HEADERS.items():
            response.headers[header] = value

        return response


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan events."""
    # Startup
    logger.info("Starting SeeMySpace API...")
    try:
        init_sentry(traces_sample_rate=0.3)
        logger.info(f"Sentry initialized")
    except Exception as e:

        logger.error(f"[STARTUP] ERROR: Failed to initialize Sentry: {str(e)}")

    # Initialize Redis connection
    try:
        redis_client.init()
        logger.info(" Redis connection initialized successfully")
    except Exception as e:
        logger.error(" Failed to initialize Redis connection")

    yield

    # Shutdown
    logger.info(" Shutting down SeeMySpace API...")

    # Close Redis connection
    redis_client.close()


# Initialize FastAPI app
app = FastAPI(
    title=constants.PROJECT_NAME,
    version=constants.VERSION,
    lifespan=lifespan,
)

# Add Request Logging Middleware (FIRST - to capture all requests)
app.add_middleware(RequestLoggingMiddleware)

# Add Security Headers Middleware (SECOND)
app.add_middleware(SecurityHeadersMiddleware)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=constants.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=constants.CORS_ALLOWED_METHODS,
    allow_headers=constants.CORS_ALLOWED_HEADERS,
    expose_headers=["*"],
)

print("hyyyyyyyyy")
# Include routers
app.include_router(auth_router)
app.include_router(user_management_router)
app.include_router(faq_router)
app.include_router(furniture_store_router)
app.include_router(product_router)
app.include_router(support_ticket_router)
