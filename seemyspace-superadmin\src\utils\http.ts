import axios, { type AxiosResponse } from "axios";

import { customError } from "../constants/commonConstant";
import { toastError } from "./toast";
import config from "../config/config";
import { store } from "../redux/store";
import { selectAuthData } from "../redux/features/authSlice";


const endPoint = config.baseUrl;

export const http = axios.create({
  baseURL: endPoint,
  headers: { "Content-Type": "application/json" },
});

console.log("Base URL", http.defaults.baseURL);

// Request interceptor to add auth token
http.interceptors.request.use(
  async (req) => {
    // Get auth token from Redux store
    const state = store.getState();
    const authData = selectAuthData(state);
    const token = authData?.token;

    if (token && req.headers) {
      req.headers.authorization = `Bearer ${token}`;
    }
    return req;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
http.interceptors.response.use(
  (response) => response.data,
  (error) => {
    console.log("Error in response interceptor", error);
    // Global error handling for all status codes
    if (error.response?.status) {
      return Promise.reject(error.response?.data);
    }

    // For network errors or other issues
    return Promise.reject({
      message: "Network error. Please check your connection.",
      status: 0,
      success: false,
    });
  }
);

export function get<P, R>(url: string, params?: P): Promise<R> {
  return http({
    method: "get",
    url,
    params,
  });
}

export function post<D, P, R>(url: string, data: D, params?: P): Promise<R> {
  return http({
    method: "post",
    url,
    data,
    params,
  });
}

export function postFile<D, P, R>(url: string, data: D, params?: P): Promise<AxiosResponse<R>> {
  return http({
    method: "post",
    url,
    data,
    params,
    headers: { "Content-Type": "multipart/form-data" },
  });
}

export function put<D, P, R>(url: string, data: D, params?: P): Promise<R> {
  return http({
    method: "put",
    url,
    data,
    params,
  });
}

export function patch<D, P, R>(url: string, data: D, params?: P): Promise<AxiosResponse<R>> {
  return http({
    method: "patch",
    url,
    data,
    params,
  });
}

export function remove<P, R>(url: string, params?: P): Promise<R> {
  return http({
    method: "delete",
    url,
    params,
  });
}

/**
 * Helper function to handle API errors consistently
 * @param error - Error object from API
 * @param translate - Translation function (i18n.t)
 * @param screen - Optional screen name for logging
 */
export const handleApiError = (error: unknown, translate: (key: string) => string, screen?: string): void => {
  console.log(`Error in handleApiError in screen : ===${screen}`, error);
  // Check if error has the expected structure
  if (error && typeof error === "object" && "message" in error && "status" in error) {
    const errorData = error as { message: string; status: number; success: boolean };

    // Handle 401 - Unauthorized (logout user)
    if (errorData.status === 401) {
      toastError(translate(customError.session_expired));
      // TODO: Implement logout logic
      // removeAuthToken();
      // window.location.href = ROUTES.LOGIN;
      return;
    }

    // Show error message if available, otherwise show generic message
    const errorMessage = translate(errorData.message) || translate(customError.something_went_wrong);
    toastError(errorMessage);
    return;
  }

  // Fallback for unexpected error structure
  toastError(translate(customError.something_went_wrong));
};
