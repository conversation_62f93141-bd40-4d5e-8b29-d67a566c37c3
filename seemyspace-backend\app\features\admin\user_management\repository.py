from typing import List, Tuple, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, cast, String
from typing import List, Tuple
from app.models.user import User ,AccountType
from app.models import UserStatus
from app.models.furniture_store import  FurnitureStore
from app.utils.auth_utils import hash_password, verify_password
from app.core.response import ResponseModal
from app.features.admin.user_management.schemas import ChangePassword
from app.utils.constants import RESPONSE_MESSAGES
from datetime import datetime
from app.core.logging_config import get_logger, setup_logging
import sentry_sdk
from sqlalchemy.exc import IntegrityError
from fastapi import status

setup_logging(level="DEBUG")
logger = get_logger(__name__)


class AdminRepository:
    """Repository for authentication-related database operations."""

    async def get_user_by_email(self, db: AsyncSession, email: str) -> User | None:
        """
        Retrieve a user from the database by email address.

        Args:
            db: Database session
            email: User's email address to search for

        Returns:
            User object if found, None otherwise
        """
        try:
            stmt = select(User).where(User.email == email.lower())
            result = await db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Unexpected error during user lookup: {e}")
            sentry_sdk.capture_exception(e)
            return None
    
    # ----------------- USER MANAGEMENT ----------------- #


    async def get_recent_users(
        self,
        db: AsyncSession,
        page: int = 1,
        size: int = 2
    ) -> ResponseModal:
        """
        Fetch recently created non-admin users with pagination support
        (used for infinite scroll in frontend).
        """
        try:
            # Ensure valid pagination values
            if page < 1:
                page = 1
            if size < 1:
                size = 10

            offset = (page - 1) * size
            logger.info(f"Fetching recent users | page={page}, size={size}")

            # Count total non-admin users
            total_stmt = select(func.count(User.id)).where(User.account_type != AccountType.ADMIN)
            total_result = await db.execute(total_stmt)
            total_users = total_result.scalar() or 0

            # Fetch paginated users (newest first)
            stmt = (
                select(User)
                .where(User.account_type != AccountType.ADMIN)
                .order_by(User.created_at.desc())
                .offset(offset)
                .limit(size)
            )
            result = await db.execute(stmt)
            users = result.scalars().all()

            # Prepare user data
            users_data = [
                {
                    "id": user.id,
                    "name": user.name,
                    "email": user.email,
                    "phone_number": user.phone_number,
                    "account_type": getattr(user.account_type, "value", user.account_type),
                    "status": getattr(user.status, "value", user.status),
                    "created_at": user.created_at.isoformat(),
                    "updated_at": user.updated_at.isoformat(),
                }
                for user in users
            ]

            # Determine if next page exists
            has_next = (page * size) < total_users

            # ✅ Successful response
            return ResponseModal(
                success=True,
                message="Recent users fetched successfully.",
                status_code=status.HTTP_200_OK,
                data={
                    "users": users_data,
                    "page": page,
                    "size": size,
                    "total": total_users,
                    "has_next": has_next
                },
            )

        except Exception as e:
            logger.error(f"Error fetching recent users: {e}", exc_info=True)
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message=f"Error fetching recent users: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    async def get_all_users(
        self,
        db: AsyncSession,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        account_type: Optional[str] = None,
        status_type: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        sort_order: Optional[str] = "desc",  
    ) -> ResponseModal:
        """
        Fetch all users with pagination, search (name/email),
        filters (account type, status, date range), and sorting.
        """
        try:
            logger.info(
                f"Fetching users | page={page} | size={size} | search={search} | "
                f"account_type={account_type} | status_type={status_type} | "
                f"date_from={date_from} | date_to={date_to} | sort_order={sort_order}"
            )

            # 🧱 Base query (exclude admin)
            base_query = select(User).where(User.account_type != AccountType.ADMIN)

            # 🔍 Search by name or email
            if search:
                search_pattern = f"%{search.lower()}%"
                base_query = base_query.where(
                    or_(
                        func.lower(User.name).like(search_pattern),
                        func.lower(User.email).like(search_pattern),
                    )
                )

            # 🧩 Filter: Account Type
            if account_type:
                base_query = base_query.where(
                    func.lower(cast(User.account_type, String)) == account_type.lower()
                )

            # Filter: Status
            if status_type:
                base_query = base_query.where(
                    func.lower(cast(User.status, String)) == status_type.lower()
                )

            # Filter: Date Range
            if date_from:
                # Set time to start of day (00:00:00)
                date_from = date_from.replace(hour=0, minute=0, second=0, microsecond=0)
                base_query = base_query.where(User.created_at >= date_from)
            if date_to:
                # Set time to end of day (23:59:59.999999)
                date_to = date_to.replace(hour=23, minute=59, second=59, microsecond=999999)
                base_query = base_query.where(User.created_at <= date_to)

            # Sorting by created_at
            if sort_order.lower() == "asc":
                base_query = base_query.order_by(User.created_at.asc())
            else:
                base_query = base_query.order_by(User.created_at.desc())

            # 🧮 Count total (without pagination)
            count_query = base_query.with_only_columns(func.count()).order_by(None)
            total_result = await db.execute(count_query)
            total_count = total_result.scalar() or 0

            # 📄 Pagination
            base_query = base_query.offset((page - 1) * size).limit(size)
            result = await db.execute(base_query)
            users = result.scalars().all()

            # 🧹 Format Response
            users_data = [
                {
                    "id": user.id,
                    "name": user.name,
                    "email": user.email,
                    "phone_number": user.phone_number,
                    "account_type": getattr(user.account_type, "value", user.account_type),
                    "status": getattr(user.status, "value", user.status),
                    "created_at": user.created_at.isoformat(),
                    "updated_at": user.updated_at.isoformat(),
                }
                for user in users
            ]

            # Determine if next page exists
            has_next = (page * size) < total_count

            return ResponseModal(
                success=True,
                message="Users fetched successfully.",
                status_code=status.HTTP_200_OK,
                data={
                    "total": total_count,
                    "page": page,
                    "size": size,
                    "has_next": has_next,
                    "users": users_data,
                },
            )

        except Exception as e:
            await db.rollback()
            logger.error(f" Error fetching", exc_info=True)
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message=f"Error fetching user: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


   

    async def get_user_by_id(self, db: AsyncSession, user_id: int) -> ResponseModal:
        """
        Fetch a user by ID.
        If account_type == FURNITURE_HOUSE_ADMIN, include furniture store details.
        """
        try:
            logger.info(f"Fetching user by id={user_id}")

            # 🔹 Fetch user from DB
            user = await db.get(User, user_id)
            if not user:
                return ResponseModal(
                    success=False,
                    message="User not found.",
                    status_code=status.HTTP_404_NOT_FOUND,
                )

            # 🔹 Base user data
            user_data = {
                "id": user.id,
                "name": user.name,
                "email": user.email,
                "account_type": (
                    user.account_type.value if hasattr(user.account_type, "value") else user.account_type
                ),
                "status": (
                    user.status.value if hasattr(user.status, "value") else user.status
                ),
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
                "profile_image": user.profile_image,
            }

            # 🔹 Check account type (match enum or raw string)
            account_type_str = (
                user.account_type.value
                if hasattr(user.account_type, "value")
                else str(user.account_type)
            ).upper()

            if account_type_str == "FURNITURE_HOUSE_ADMIN":
                logger.info("Fetching furniture store details for user")
                result = await db.execute(
                    select(FurnitureStore).where(FurnitureStore.user_id == user.id)
                )
                store = result.scalar_one_or_none()
                if store:
                    user_data["furniture_store_details"] = {
                        "store_id": store.store_id,
                        "contact_person_name": store.contact_person_name,
                        "contact_person_email": store.contact_person_email,
                        "contact_person_phone": store.contact_person_phone,
                        "country_code": store.country_code,
                        "created_at": store.created_at.isoformat() if store.created_at else None,
                        "updated_at": store.updated_at.isoformat() if store.updated_at else None,
                    }
                else:
                    user_data["furniture_store_details"] = None

            logger.info(f" Final User Response: {user_data}")

            return ResponseModal(
                success=True,
                message="User fetched successfully.",
                status_code=status.HTTP_200_OK,
                data=user_data,
            )

        except Exception as e:
            await db.rollback()
            logger.error(f" Error fetching user by id={user_id}: {e}", exc_info=True)
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message=f"Error fetching user: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    async def change_password(self, db: AsyncSession, user: ChangePassword) -> ResponseModal:
        """
        Change user password after verifying the old password.

        Args:
            db: Database session
            user: ChangePassword schema with email, old_password, and new_password

        Returns:
            ResponseModal with success status
        """
        try:
            #  1. Check if user exists
            existing_user = await self.get_user_by_email(db, user.email)
            if not existing_user:
                return ResponseModal(
                    success=False,
                    message=RESPONSE_MESSAGES["User_Not_Exist"],
                    status_code=status.HTTP_404_NOT_FOUND
                )

            #  2. Verify old password
            if not verify_password(user.old_password, existing_user.password):
                return ResponseModal(
                    success=False,
                    message=RESPONSE_MESSAGES["Invalid_Password"],
                    status_code=status.HTTP_401_UNAUTHORIZED
                )

            #  3. Prevent same password reuse
            if verify_password(user.new_password, existing_user.password):
                return ResponseModal(
                    success=False,
                    message="New password cannot be the same as the old password.",
                    status_code=status.HTTP_400_BAD_REQUEST
                )


            #  5. Hash and update password
            existing_user.password = hash_password(user.new_password)
            existing_user.updated_at = datetime.utcnow()
            await db.commit()

            return ResponseModal(
                success=True,
                message="Password changed successfully.",
                status_code=status.HTTP_200_OK
            )

        except Exception as e:
            await db.rollback()
            logger.error(f"Unexpected error during password change: {e}")
            return ResponseModal(
                success=False,
                message=RESPONSE_MESSAGES["Went_Wrong"],
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        

    async def change_user_status(self, db: AsyncSession, user_id: int) -> ResponseModal:
        """Toggle user status between ACTIVE and INACTIVE."""
        try:
            user = await db.get(User, user_id)
            if not user:
                return ResponseModal(
                    success=False,
                    message="User not found.",
                    status_code=status.HTTP_404_NOT_FOUND,
                )

            # ✅ Toggle status using Enum
            user.status = (
                UserStatus.INACTIVE if user.status == UserStatus.ACTIVE else UserStatus.ACTIVE
            )
            user.updated_at = datetime.utcnow()

            await db.commit()
            await db.refresh(user)

            return ResponseModal(
                success=True,
                message=f"User status changed to {user.status.value}.",
                status_code=status.HTTP_200_OK,
                data={"id": user.id, "status": user.status.value},
            )

        except Exception as e:
            await db.rollback()
            logger.error(f"Error toggling user status: {e}", exc_info=True)
            return ResponseModal(
                success=False,
                message=f"Error toggling user status: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    async def delete_user(self, db: AsyncSession, user_id: int) -> ResponseModal:
        """
        Delete a user by ID.
        If the user is a Furniture House Admin, also delete associated furniture store data.
        
        Args:
            db: Database session
            user_id: ID of the user to delete
            
        Returns:
            ResponseModal with success status
        """
        try:
            logger.info(f"Deleting user by id={user_id}")
            
            # Fetch user from DB
            user = await db.get(User, user_id)
            if not user:
                return ResponseModal(
                    success=False,
                    message="User not found.",
                    status_code=status.HTTP_404_NOT_FOUND,
                )
            
            # Check if user is a Furniture House Admin
            account_type_str = (
                user.account_type.value
                if hasattr(user.account_type, "value")
                else str(user.account_type)
            ).upper()
            
            # If user is a Furniture House Admin, delete associated furniture store data
            if account_type_str == "FURNITURE_HOUSE_ADMIN":
                logger.info("Deleting furniture store details for user")
                result = await db.execute(
                    select(FurnitureStore).where(FurnitureStore.user_id == user.id)
                )
                store = result.scalar_one_or_none()
                if store:
                    await db.delete(store)
            
            # Delete the user
            await db.delete(user)
            await db.commit()
            
            return ResponseModal(
                success=True,
                message="User deleted successfully.",
                status_code=status.HTTP_200_OK,
            )

        except Exception as e:
            await db.rollback()
            logger.error(f"Error deleting user by id={user_id}: {e}", exc_info=True)
            sentry_sdk.capture_exception(e)
            return ResponseModal(
                success=False,
                message=f"Error deleting user: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )