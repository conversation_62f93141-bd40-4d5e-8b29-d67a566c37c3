import Modal from "../common/Modal";
import Button from "../formElements/Button";

interface LogoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLogout: () => void;
}

const LogoutModal = ({ isOpen, onClose, onLogout }: LogoutModalProps) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Logout Confirmation"
      description="Are you sure you want to logout?"
      size="md"
    >
      <div className="button-align gap-3">
          <Button 
            className="primary-btn rounded-md w-100" 
            onClick={onLogout}
          >
            Yes
          </Button>
          <Button 
            className="dark-outline-btn rounded-md w-100" 
            onClick={onClose}
          >
            No
          </Button>
       
      </div>
    </Modal>
  );
};

export default LogoutModal;
