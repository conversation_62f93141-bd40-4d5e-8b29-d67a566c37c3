@use "../abstracts" as *;

//common input style ----------
.form-group {
  margin-bottom: 20px;
  position: relative;
  label {
    font-size: $text-sm;
    margin-bottom: 5px;
    color: $dark;
    font-weight: 500;
    sup {
      color: $danger;
    }
  }
  .form-control,
  select {
    border-radius: 10px;
    border: 1px solid rgba($dark, 0.1);
    background: $white;
    box-shadow: 0 1px 2px 0 rgba(228, 229, 231, 0.24);
    padding: 10px 12px;
    font-size: $text-md;
    color: $dark;
    resize: none;
    caret-color: $dark;
    font-weight: 300;
    &::placeholder {
      color: #808080;
    }

    &:-webkit-autofill {
      -webkit-text-fill-color: $dark !important;
    }

    &:disabled {
      background: transparent;
    }

    &:focus {
      outline: none;
      box-shadow: none;
      border: 1px solid rgba($white, 0.6);
      background: $grey;
      color: $dark;
    }
  }
  .auth-msg {
    font-size: $text-xs;
    margin: 5px 0 0;
    font-weight: 400;
    &.error {
      color: $danger !important;
    }
  }
  .icon-align {
    position: relative;
    input {
      padding-right: 46px;
    }
    &.left {
      input {
        padding-left: 46px;
      }
      img,
      button,
      svg {
        left: 25px;
      }
    }
    &.right {
      img {
        right: 5px;
      }
    }
    .show-icon {
      background-color: transparent;
      border: none;
      padding: 0;
      position: absolute;
      top: 50%;
      right: 0px;
      transform: translate(-50%, -50%);
      width: 25px;
      height: 25px;
      img,
      svg {
        width: 20px;
        height: 20px;
        object-fit: contain;
      }
    }
  }
}

select {
  cursor: pointer;
  -moz-appearance: none; /* Firefox */
  -webkit-appearance: none; /* Safari and Chrome */
  appearance: none;
  background: #1e1e1e !important;
  option {
    color: $dark;
  }
}

/* Chrome, Opera */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition: background-color 5000s ease-in-out 0s;
  -webkit-text-fill-color: $white !important;
}

/* Firefox */
input:-moz-autofill,
input:-moz-autofill:hover,
input:-moz-autofill:focus,
input:-moz-autofill:active {
  transition: background-color 5000s ease-in-out 0s;
  color: $white !important;
}

/* Safari */
input:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s;
  -webkit-text-fill-color: $white !important;
}

// //upload box style ----------
.upload-box {
  border: 1px solid rgba($white, 0.6);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: $white;
  position: relative;
  .upload-para {
    font-weight: 500;
    margin: 10px 0;
  }
  .browse {
    text-align: center;
    color: rgba($white, 0.6);
    margin: 0;
    span {
      color: $dark;
      font-weight: 600;
      text-decoration: underline;
    }
  }
  input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}

//upload-images-card
.upload-images-card {
  border: 1px solid rgba($white, 0.6);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  input {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
  }

  .card-inner {
    p {
      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
      color: rgba($white, 0.6);
      margin: 0px;
    }
  }
}

//uploaded-images-box
.uploaded-images-box {
  padding-top: 10px;

  .name {
    h4 {
      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
      color: rgba($white, 0.6);
      margin: 0px 0px 10px;
    }
  }

  .uploaded-images-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;

    .uploaded-image-item {
      position: relative;
      display: inline-block;

      img {
        width: 90px;
        height: 90px;
        min-width: 90px;
        border-radius: 16px;
        border: 3px solid transparent;
        object-fit: cover;
        object-position: top;
      }

      .close-btn {
        padding: 0px;
        background-color: rgba(0, 0, 0, 0.7);
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 1;
        border-radius: 100px;
        &.close-btn-border {
          svg {
            border: 1px solid rgba($white, 0.6);
            border-radius: 100%;
          }
        }
      }

      .item-radio {
        position: absolute;
        left: 0px;
        top: 0px;
        width: 100%;
        height: 100%;
        cursor: pointer;
        opacity: 0;
      }

      .item-radio:checked + img {
        border-color: $primary;
      }
    }
  }
}

// custom-checkbox
.custom-checkbox {
  display: block;
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: $dark;
  font-size: $text-sm;
  font-weight: 500;
  line-height: 20px;
  position: relative;

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 100%;
    width: 100%;
    z-index: 1;
    left: 0px;
    top: 0px;
  }

  .checkmark {
    position: absolute;
    top: -3px;
    left: 0;
    height: 22px;
    width: 22px;
    background-color: transparent;
    border: 2px solid #4f4f4f;
    border-radius: 8px;
  }

  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }

  input:checked ~ .checkmark:after {
    display: block;
  }

  .checkmark:after {
    left: 6px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid #4f4f4f;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }
}

//react phone input style ------------
.react-tel-input .form-control {
  background: transparent !important;
  border-radius: 0 !important;
  padding-block: 21.5px;
}

.otp-main {
  margin-bottom: 10px;

  div {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
  }

  input {
    text-align: center;
    background-color: transparent;
    border-radius: 0px;
    color: $white;
    font-size: 14px;
    height: 47px;
    width: 100% !important;
    border: 1px solid rgba($white, 0.6);
  }

  // .common-otp {
  //  width: 100%;
  //  margin: 0 5px;

  //  &.width-custom {
  //      input {
  //          width: 50px !important;
  //      }
  //  }
  // }
}

.uploaded-images-box {
  padding-top: 10px;

  .name {
    h4 {
      font-size: 16px;
      font-weight: 400;
      line-height: 20px;
      color: #f9f9f9;
      margin: 0px 0px 10px;
    }
  }

  .uploaded-images-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;

    .uploaded-image-item {
      position: relative;
      display: inline-block;

      img {
        width: 90px;
        height: 90px;
        min-width: 90px;
        border-radius: 16px;
        border: 3px solid transparent;
        object-fit: cover;
        object-position: top;
      }

      .pdf-preview {
        width: 90px;
        height: 90px;
        min-width: 90px;
        border-radius: 16px;
        border: 3px solid transparent;
        background-color: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        span {
          font-size: 14px;
          color: #333;
          padding: 10px;
        }
      }

      .close-btn {
        padding: 0px;
        background-color: rgba(0, 0, 0, 0.7);
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 1;
        border-radius: 100px;
        &.close-btn-border {
          svg {
            border: 1px solid #f9f9f9;
            border-radius: 100%;
          }
        }
      }

      .item-radio {
        position: absolute;
        left: 0px;
        top: 0px;
        width: 100%;
        height: 100%;
        cursor: pointer;
        opacity: 0;
      }

      .item-radio:checked + img {
        border-color: #cea344;
      }
    }
  }
}

.selected-flag {
  background-color: $dark;
}

//switch style ----------
.form-check-input:checked {
  background-color: $primary !important;
  border-color: $primary !important;
}

.form-check-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(186, 62, 62, 0.25) !important;
}

// Status Select Dropdown
.down-icon {
  position: relative;
  display: inline-flex;
  align-items: center;

  select {
    appearance: none;
    border: none;
    background: transparent !important;
    font-size: $text-sm;
    font-weight: 500;
    padding: 6px 30px 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    outline: none;
    min-width: 110px;

    &:focus {
      outline: none;
      box-shadow: none;
    }

    &.status-pending {
      color: #7d7050;
    }

    &.status-accepted {
      color: $success;
    }

    &.status-rejected {
      color: $danger;
    }
  }

  svg {
    position: absolute;
    right: 8px;
    pointer-events: none;
    width: 12px;
    height: 12px;

    path {
      stroke: currentColor;
    }
  }
}
