import json
import logging
from functools import lru_cache
from typing import Any, Dict, Optional

import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from sentry_sdk import capture_exception

from app.core.config.config import get_env_variables

logger = logging.getLogger(__name__)

# Global variables for caching
_secret_keys: Dict[str, Any] = None


def get_secret_keys(is_force_sync=False) -> Dict[Any, Any]:
    """
    Fetch and cache secrets from AWS Secret Manager.

    Args:
        is_force_sync: Force refresh the cached secrets

    Returns:
        dict: Cached secrets.
    """
    global _secret_keys

    if _secret_keys is None or is_force_sync:
        try:
            env_vars = get_env_variables()
            secret_manager_name = env_vars.get("SECRET_MANAGER_NAME")
            mode = env_vars.get("MODE")
            region = env_vars.get("AWS_REGION") or "us-west-2"
            profile_name = env_vars.get("PROFILE_NAME")

            if not secret_manager_name:
                logger.warning("SECRET_MANAGER_NAME not found in environment variables")
                _secret_keys = {}
                return _secret_keys

            if mode == "local" and profile_name:
                # Use AWS CLI profile credentials when in local mode
                session = boto3.Session(profile_name=profile_name, region_name=region)
                logger.info(f"Using AWS profile: {profile_name} for secrets manager")
            else:
                # Use instance profile or environment credentials
                session = boto3.Session(region_name=region)
                logger.info("Using instance profile or environment credentials for secrets manager")

            client = session.client("secretsmanager")
            response = client.get_secret_value(SecretId=secret_manager_name)
            json_data = response["SecretString"]
            _secret_keys = json.loads(json_data)
            logger.info("Successfully retrieved secrets from AWS Secret Manager")
        except NoCredentialsError as e:
            logger.warning(f"AWS credentials not found: {e}")
            capture_exception(e)
            _secret_keys = {}
        except ClientError as e:
            logger.warning(f"AWS Secret Manager error: {e}")
            capture_exception(e)
            _secret_keys = {}
        except Exception as e:
            logger.warning(f"Error retrieving secrets: {e}")
            capture_exception(e)
            _secret_keys = {}

    return _secret_keys or {}


def get_secret(key: str) -> Optional[str]:
    """
    Retrieve a specific secret from AWS Secret Manager.

    Args:
        key: The key of the secret to retrieve.

    Returns:
        The secret value or None if not found.
    """
    try:
        # Create AWS session with proper credentials
        env_vars = get_env_variables()
        mode = env_vars.get("MODE")
        region = env_vars.get("AWS_REGION") or "us-west-2"
        profile_name = env_vars.get("PROFILE_NAME")

        if mode == "local" and profile_name:
            # Use AWS CLI profile credentials when in local mode
            session = boto3.Session(profile_name=profile_name, region_name=region)
        else:
            # Use instance profile or environment credentials
            session = boto3.Session(region_name=region)

        client = session.client("secretsmanager")
        get_secret_value_response = client.get_secret_value(SecretId=key)
        secret = get_secret_value_response["SecretString"]
        return secret
    except Exception as e:
        logger.error(f"Error retrieving secret {key}: {e}")
        capture_exception(e)
        return None


@lru_cache()
def get_cached_secrets() -> dict:
    """
    Fetch and cache secrets from AWS Secret Manager.
    This is a cached version for backward compatibility.

    Returns:
        dict: Cached secrets.
    """
    return get_secret_keys()
