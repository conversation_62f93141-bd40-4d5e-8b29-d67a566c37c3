import re
from typing import Optional, List

from pydantic import BaseModel, Field, field_validator
from datetime import datetime


class CreateSupportTicket(BaseModel):
    """
    Schema for creating a new support ticket.

    Attributes:
        support_ticket_categories_id (int): Category ID for the ticket
        subject (str): Subject of the ticket
        message (str): Detailed message describing the issue
        images (Optional[List[str]]): Optional list of image URLs
        additional_info (Optional[dict]): Optional additional information
    """

    support_ticket_categories_id: int = Field(
        ..., 
        gt=0,
        example=1,
        description="Support ticket category ID"
    )
    subject: str = Field(
        ..., 
        min_length=1, 
        max_length=50,
        example="Unable to view property details"
    )
    message: str = Field(
        ..., 
        min_length=1,
        example="I'm experiencing issues when trying to view property details..."
    )
    images: Optional[List[str]] = Field(
        default=None,
        example=["https://example.com/image1.jpg"],
        description="List of image URLs (max 5MB per image)"
    )
   

    @field_validator("support_ticket_categories_id")
    @classmethod
    def validate_category_id(cls, v):
        if v <= 0:
            raise ValueError("Category ID must be a positive integer")
        return v


class SupportTicketIdSchema(BaseModel):
    """
    Schema for support ticket ID validation.

    Attributes:
        ticket_id (int): Support ticket ID must be a positive integer.
    """

    ticket_id: int = Field(
        ..., 
        gt=0,
        description="Support ticket ID must be a positive integer"
    )

    @field_validator("ticket_id")
    @classmethod
    def validate_ticket_id(cls, v):
        if v <= 0:
            raise ValueError("Support ticket ID must be a positive integer")
        return v


class SupportTicketFilterParams(BaseModel):
    """
    Schema for support ticket filtering and pagination parameters.
    
    Attributes:
        page (int): Page number (0-indexed)
        limit (int): Number of items per page
        search (Optional[str]): Search by user name, ticket ID, or subject
        user_type (Optional[str]): Filter by user account type (admin, agent, buyer, furniture_house_admin)
        status (Optional[str]): Filter by ticket status (pending, resolved)
        category_id (Optional[int]): Filter by support ticket category ID
        start_date (Optional[datetime]): Filter tickets created on or after this date
        end_date (Optional[datetime]): Filter tickets created on or before this date
    """
    
    page: int = Field(default=0, ge=0, description="Page number (0-indexed)")
    limit: int = Field(default=10, ge=1, le=100, description="Number of items per page")
    search: Optional[str] = Field(default=None, description="Search by user name, ticket ID, or subject")
    user_type: Optional[str] = Field(default=None, description="Filter by user type (admin, agent, buyer, furniture_house_admin)")
    status: Optional[str] = Field(default=None, description="Filter by status (pending, resolved)")
    category_id: Optional[int] = Field(default=None, ge=1, description="Filter by category ID")
    start_date: Optional[datetime] = Field(default=None, description="Start date (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)")
    end_date: Optional[datetime] = Field(default=None, description="End date (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)")


class SupportTicketCategoryResponse(BaseModel):
    """
    Schema for support ticket category creation and validation.
    
    Attributes:
        name (str): Name of the support ticket category
        
    Validation Rules:
        - Required field
        - Length: 3-50 characters
        - Allowed: letters, numbers, spaces, hyphens
        - No leading/trailing spaces
        - Cannot be only numbers
        - Must be unique (case insensitive)
    """
    
    name: str = Field(
        ..., 
        min_length=3, 
        max_length=50,
        example="Technical Issues",
        description="Name of the support ticket category"
    )

    @field_validator("name")
    @classmethod
    def validate_category_name(cls, v: str) -> str:
        # Trim spaces from both ends
        v = v.strip()
        
        # Check minimum length after trimming
        if len(v) < 3:
            raise ValueError("Category name must be at least 3 characters long")
            
        # Check for valid characters
        pattern = r"^[a-zA-Z\s\-]+$"
        if not re.match(pattern, v):
            raise ValueError("Category name can only contain letters, spaces, and hyphens")
            
        # Check if empty after removing spaces
        if not v.replace(" ", ""):
            raise ValueError("Category name cannot be empty or contain only spaces")
            
        return v
    
    model_config = {
        "from_attributes": True
    }

class SupportTicketCategoryIdSchema(BaseModel):
    """
    Schema for support ticket category ID validation.
    
    Attributes:
        category_id (int): Support ticket category ID must be a positive integer
    """
    
    category_id: int = Field(
        ..., 
        gt=0,
        description="Support ticket category ID must be a positive integer"
    )
    
    @field_validator("category_id")
    @classmethod
    def validate_category_id(cls, v):
        if v <= 0:
            raise ValueError("Support ticket category ID must be a positive integer")
        return v

