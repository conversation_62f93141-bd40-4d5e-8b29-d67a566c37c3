@use "../../styles/abstracts" as *;

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(135, 95, 69, 0.1);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-container {
  background-color: $white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-width: 90%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
  padding: 30px;
  &.modal-sm {
    width: 400px;
  }

  &.modal-md {
    width: 520px;
  }

  &.modal-lg {
    width: 700px;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  padding: 24px 24px 20px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 20px;
  .modal-title {
    font-size: $text-lg;
    font-weight: 600;
    color: $dark;
    margin: 0;
  }
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

// Responsive
@media (max-width: 768px) {
  .modal-container {
    width: 100% !important;
    max-width: 95%;
    margin: 0 auto;
  }
}
