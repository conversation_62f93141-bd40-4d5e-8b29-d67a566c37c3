import React, { useEffect, useRef } from "react";
import "./Modal.scss";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?:string;
  children: React.ReactNode;
  size?: "sm" | "md" | "lg";
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, description,children, size = "md" }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  	useEffect(() => {
		if (!isOpen) return;

		const modalElement = modalRef.current;
		if (!modalElement) return;

		// Get all focusable elements within the modal
		const getFocusableElements = (): HTMLElement[] => {
			const focusableSelectors = [
				"a[href]",
				"button:not([disabled])",
				"textarea:not([disabled])",
				"input:not([disabled])",
				"select:not([disabled])",
				"[tabindex]:not([tabindex='-1'])",
			].join(", ");

			return Array.from(
				modalElement.querySelectorAll<HTMLElement>(focusableSelectors)
			).filter(
				(el) => !el.hasAttribute("disabled") && el.offsetParent !== null
			);
		};

		// Handle tab key press to trap focus
		const handleKeyDown = (e: KeyboardEvent) => {
			if (e.key !== "Tab") return;

			const focusableElements = getFocusableElements();
			if (focusableElements.length === 0) return;

			const firstElement = focusableElements[0];
			const lastElement = focusableElements[focusableElements.length - 1];

			// Shift + Tab (backwards)
			if (e.shiftKey) {
				if (document.activeElement === firstElement) {
					e.preventDefault();
					lastElement.focus();
				}
			}
			// Tab (forwards)
			else {
				if (document.activeElement === lastElement) {
					e.preventDefault();
					firstElement.focus();
				}
			}
		};

		// Focus first focusable element when modal opens
		const focusableElements = getFocusableElements();
		if (focusableElements.length > 0) {
			focusableElements[0].focus();
		}

		// Add event listener
		modalElement.addEventListener("keydown", handleKeyDown);

		// Cleanup
		return () => {
			modalElement.removeEventListener("keydown", handleKeyDown);
		};
	}, [isOpen]);

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleBackdropClick} ref={modalRef}>
      <div className={`modal-container modal-${size}`}>
        <div className="modal-header">
          <h3 className="modal-title">{title}</h3>
        </div>
          <p className="modal-title">{description}</p>
        <div className="modal-body">{children}</div>
      </div>
    </div>
  );
};

export default Modal;
