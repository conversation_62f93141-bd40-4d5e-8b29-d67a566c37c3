/**
 * Support Ticket Category Interfaces
 * Defines types for category operations
 */

/**
 * Support ticket category data structure
 */
export interface ISupportTicketCategory {
  id: number;
  name: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

/**
 * Request payload for creating a support ticket category
 */
export interface ICreateSupportTicketCategory {
  name: string;
}

/**
 * Request payload for updating a support ticket category
 */
export interface IUpdateSupportTicketCategory {
  name: string;
}

/**
 * Response from category list API
 */
export interface ISupportTicketCategoryListResponse {
  id: number;
  name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Form data for add/edit category modal
 */
export interface ICategoryFormData {
  categoryName: string;
}

