{"name": "superadmin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "env-cmd -f .env vite", "dev:development": "env-cmd -f .env.development vite", "build": "env-cmd -f .env tsc && vite build", "build:development": "env-cmd -f .env.development tsc && vite build", "lint-check": "eslint .", "lint-fix": "eslint . --fix", "prettier-check": "prettier --check .", "prettier-fix": "prettier --write .", "type-check": "tsc --noEmit", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@reduxjs/toolkit": "^2.9.2", "@types/react-datepicker": "^6.2.0", "axios": "^1.13.0", "bootstrap": "^5.3.8", "env-cmd": "^11.0.0", "i18next": "^25.6.0", "i18next-http-backend": "^3.0.2", "react": "^19.1.1", "react-datepicker": "^8.8.0", "react-dom": "^19.1.1", "react-hook-form": "^7.65.0", "react-i18next": "^16.2.1", "react-number-format": "^5.4.4", "react-otp-input": "^3.1.1", "react-redux": "^9.2.0", "react-router-dom": "^7.9.4", "react-toastify": "^11.0.5", "recharts": "^3.3.0", "redux-persist": "^6.0.0", "sass": "^1.93.2", "yup": "^1.7.1"}, "devDependencies": {"@eslint/js": "^9.38.0", "@types/node": "^24.9.1", "@types/react": "^19.2.2", "@types/react-dom": "^19.2.2", "@vitejs/plugin-react": "^5.0.4", "eslint": "^9.38.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.24", "eslint-plugin-unused-imports": "^4.3.0", "globals": "^16.4.0", "husky": "^9.1.7", "prettier": "^3.6.2", "typescript": "~5.9.3", "typescript-eslint": "^8.46.2", "vite": "^7.1.7"}}