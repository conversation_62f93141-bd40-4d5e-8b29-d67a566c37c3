import React from "react";

class Helper {
  /**
   * Prevents space character in input fields
   * @param e Keyboard event
   */
  static preventSpaceInPassword(e: React.KeyboardEvent): void {
    if (e.key === " ") {
      e.preventDefault();
    }
  }

  /**
   * Convert a string to title case
   * @param name - The string to convert
   * @returns The string in title case
   */
  static toTitleCase = (name: string) => {
    if (!name) return "";
    return name
      .toLowerCase()
      .split(" ")
      .filter((word) => word) // remove extra spaces
      .map((word) => word[0].toUpperCase() + word.slice(1))
      .join(" ");
  };
}

export default Helper;
