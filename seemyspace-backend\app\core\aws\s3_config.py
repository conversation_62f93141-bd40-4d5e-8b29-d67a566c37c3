"""
AWS S3 configuration and utilities for file operations
"""

import base64
import io
import logging
import uuid
from typing import Tuple

import sentry_sdk
from botocore.exceptions import ClientError, NoCredentialsError, PartialCredentialsError
from fastapi import HTTPException, status

from app.core.aws.aws_config import get_aws_client, get_aws_credentials

logger = logging.getLogger(__name__)


def generate_presigned_urls(
    file_names: list, content_type: str = "image/jpeg", expiration: int = 3600
):
    """
    Generate pre-signed URLs to share S3 objects.

    Args:
        file_names (list): List of S3 object names.
        content_type (str): Content type for the files.
        expiration (int): Time in seconds for the pre-signed URLs to remain valid.

    Returns:
        list: List of pre-signed URLs as strings. If error, returns None.
    """
    try:
        # Get AWS credentials
        aws_creds = get_aws_credentials()
        if not aws_creds.get("aws_s3_bucket"):
            logger.error("AWS S3 bucket not configured")
            return None

        # Use centralized AWS client
        s3_client = get_aws_client("s3")
        urls = []

        for object_name in file_names:
            response = s3_client.generate_presigned_url(
                "put_object",
                Params={
                    "Bucket": aws_creds["aws_s3_bucket"],
                    "Key": object_name,
                    "ContentType": content_type,
                },
                ExpiresIn=expiration,
            )
            urls.append(response)

        return urls

    except (NoCredentialsError, PartialCredentialsError) as e:
        sentry_sdk.capture_exception(e)
        logger.error(f"Credentials error: {e}")
        return None
    except Exception as e:
        sentry_sdk.capture_exception(e)
        logger.error(f"Error generating pre-signed URLs: {e}")
        return None


class S3Client:
    """S3 Client for file operations"""

    def __init__(self):
        """Initialize S3 client"""
        try:
            # Use the centralized AWS client creation
            self.s3_client = get_aws_client("s3")

            # Get AWS credentials
            aws_creds = get_aws_credentials()
            self.bucket_name = aws_creds["aws_s3_bucket"]

            self.profile_image_prefix = "user-profiles/"
            # Add prefixes for chat attachments
            self.chat_image_prefix = "ChatMessage/Images/"
            self.chat_document_prefix = "ChatMessage/Documents/"
        except Exception as e:
            sentry_sdk.capture_exception(e)
            logger.error(f"Error initializing S3 client: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to initialize S3 client: {str(e)}",
            )

    def upload_base64_image(self, base64_data: str, user_id: int) -> Tuple[bool, str]:
        """
        Upload a base64 encoded image to S3

        Args:
            base64_data: Base64 encoded image string
            user_id: ID of the user to associate with the image

        Returns:
            Tuple[bool, str]: Success flag and image URL or error message
        """
        try:
            # Validate base64 data format
            if not base64_data or not isinstance(base64_data, str):
                return False, "Invalid base64 data: data is empty or not a string"

            # Remove data URL prefix if present (e.g., data:image/jpeg;base64,)
            if "," in base64_data:
                prefix, base64_data = base64_data.split(",", 1)
            else:
                logger.warning("Base64 data doesn't contain data URL format prefix")

            try:
                # Decode base64 data
                image_data = base64.b64decode(base64_data)

                # Check if decoded data is valid
                if len(image_data) < 100:  # Arbitrary small size check
                    return False, "Decoded image data is too small, likely invalid"
            except Exception as e:
                sentry_sdk.capture_exception(e)
                logger.error(f"Failed to decode base64 data: {str(e)}")
                return False, f"Failed to decode base64 data: {str(e)}"

            # Generate a unique filename
            file_extension = "jpg"  # Default to jpg
            filename = f"{user_id}_{uuid.uuid4()}.{file_extension}"
            object_key = f"{self.profile_image_prefix}{filename}"

            # Upload to S3
            try:
                self.s3_client.upload_fileobj(
                    io.BytesIO(image_data),
                    self.bucket_name,
                    object_key,
                    ExtraArgs={"ContentType": "image/jpeg"},
                )
            except ClientError as e:
                sentry_sdk.capture_exception(e)
                error_code = e.response.get("Error", {}).get("Code")
                error_message = e.response.get("Error", {}).get("Message")
                logger.error(f"S3 upload ClientError: {error_code} - {error_message}")
                return False, f"S3 upload failed: {error_code} - {error_message}"

            # Generate the image URL
            image_url = f"https://{self.bucket_name}.s3.amazonaws.com/{object_key}"

            return True, image_url

        except ClientError as e:
            sentry_sdk.capture_exception(e)
            logger.error(f"S3 client error: {str(e)}")
            return False, f"S3 client error: {str(e)}"
        except Exception as e:
            sentry_sdk.capture_exception(e)
            logger.error(f"Error uploading image: {str(e)}")
            return False, f"Error uploading image: {str(e)}"

    def delete_image(self, image_url: str) -> Tuple[bool, str]:
        """
        Delete an image from S3 using its URL

        Args:
            image_url: URL of the image to delete

        Returns:
            Tuple[bool, str]: Success flag and message
        """
        try:
            # Extract object key from URL
            if self.bucket_name not in image_url:
                return False, "Invalid image URL"

            object_key = image_url.split(f"{self.bucket_name}.s3.amazonaws.com/")[1]

            # Delete from S3
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=object_key)

            return True, "Image deleted successfully"

        except ClientError as e:
            sentry_sdk.capture_exception(e)
            logger.error(f"S3 client error: {str(e)}")
            return False, f"S3 client error: {str(e)}"
        except Exception as e:
            sentry_sdk.capture_exception(e)
            logger.error(f"Error deleting image: {str(e)}")
            return False, f"Error deleting image: {str(e)}"

# Create singleton instance
s3_client = S3Client()
