from fastapi import APIRouter, Depends, status,Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List,Optional
from datetime import datetime
from app.core.database.database import get_async_db
from app.core.response import ResponseModal
from app.features.admin.user_management.route_definitions import admin_routes
from app.features.admin.user_management.repository import AdminRepository
from app.features.admin.user_management.schemas import ChangePassword
from app.models.user import User, AccountType
from app.middleware.verify import roles_required

router = APIRouter(prefix=admin_routes.BASE, tags=["Admin Users"])
admin_repository = AdminRepository()


@router.get(
    admin_routes.GET_RECENT_USERS,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def get_recent_users(
    db: AsyncSession = Depends(get_async_db),
    page: int = Query(1, ge=1, description="Page number for pagination"),
    size: int = Query(10, ge=1, le=100, description="Number of users per page"),
) -> ResponseModal:
    """
    Fe<PERSON> recently created non-admin users with pagination support (for infinite scroll).
    """
    return await admin_repository.get_recent_users(db=db, page=page, size=size)

@router.get(
    admin_routes.GET_ALL_USERS,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def get_all_users(
    page: int = Query(1, description="Page number for pagination"),
    size: int = Query(10, description="Number of records per page"),
    search: Optional[str] = Query(None, description="Search by name or email"),
    account_type: Optional[str] = Query(None, description="Filter by account type"),
    status_type: Optional[str] = Query(None, alias="status", description="Filter by account status (e.g., ACTIVE, INACTIVE)"),
    date_from: Optional[datetime] = Query(None, description="Filter by registration start date (YYYY-MM-DD)"),
    date_to: Optional[datetime] = Query(None, description="Filter by registration end date (YYYY-MM-DD)"),
    sort_order: Optional[str] = Query("desc", description="Sort order by registration date (asc or desc)"),
    db: AsyncSession = Depends(get_async_db),
    # user: dict = Depends(roles_required([AccountType.ADMIN]))
) -> ResponseModal:
    """
    Get all users with pagination, optional search (by name/email),
    and filters for account type, status, and registration date range.
    Supports sorting by registration date.
    
    Args:
        page (int): Page number for pagination.
        size (int): Number of users per page.
        search (Optional[str]): Keyword to search in name or email.
        account_type (Optional[str]): Filter by account type.
        status_filter (Optional[str]): Filter by account status.
        date_from (Optional[datetime]): Registration start date.
        date_to (Optional[datetime]): Registration end date.
        sort_order (Optional[str]): Sort order ('asc' or 'desc').
        db (AsyncSession): Async database session dependency.

    Returns:
        ResponseModal: Standard API response with users data.
    """
    return await admin_repository.get_all_users(
        db=db,
        page=page,
        size=size,
        search=search,
        account_type=account_type,
        status_type=status_type,
        date_from=date_from,
        date_to=date_to,
        sort_order=sort_order,
    )




@router.post(
    admin_routes.CHANGE_PASSWORD,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def change_password(
            user_data: ChangePassword,
            db: AsyncSession = Depends(get_async_db),
            user: dict = Depends(roles_required([AccountType.ADMIN]))
) -> ResponseModal:
            """
            Change user password after verifying old password.

            Args:
                user (ChangePassword): Data containing email, old_password, and new_password
                db (AsyncSession): Async database session dependency

            Returns:
                ResponseModal: Response indicating whether the password was successfully changed
            """
            return await admin_repository.change_password(db, user_data)

@router.get(
    admin_routes.GET_USER_BY_ID,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def get_user_by_id(
user_id: int,
db: AsyncSession = Depends(get_async_db),
# user: dict = Depends(roles_required([AccountType.ADMIN]))
) -> ResponseModal:
    """
    Retrieve user details by ID.
    If the user is a Furniture Store Admin, include associated furniture store details.

    Args:
        user_id (int): Unique ID of the user to retrieve
        db (AsyncSession): Async database session dependency

    Returns:
        ResponseModal: Response containing user details and (if applicable) furniture store data
    """
    return await admin_repository.get_user_by_id(db, user_id)


@router.put(
    admin_routes.CHANGE_USER_STATUS,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def change_user_status(
user_id: int,
db: AsyncSession = Depends(get_async_db),
# user: dict = Depends(roles_required([AccountType.ADMIN]))
) -> ResponseModal:
    """
    change user status between ACTIVE and INACTIVE.
    """
    return await admin_repository.change_user_status(db, user_id)


@router.delete(
    admin_routes.DELETE_USER,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def delete_user(
user_id: int,
db: AsyncSession = Depends(get_async_db),
# user: dict = Depends(roles_required([AccountType.ADMIN]))
) -> ResponseModal:
    """
    Delete a user by ID.
    If the user is a Furniture House Admin, also delete associated furniture store data.

    Args:
        user_id (int): Unique ID of the user to delete
        db (AsyncSession): Async database session dependency

    Returns:
        ResponseModal: Response indicating whether the user was successfully deleted
    """
    return await admin_repository.delete_user(db, user_id)