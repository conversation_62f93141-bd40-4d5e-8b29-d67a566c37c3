"""Application configuration constants"""

# Project settings
PROJECT_NAME = "SeeMySpace"
VERSION = "1.0.0"


BACKEND_CORS_ORIGINS = ["*"]

# CORS allowed methods
CORS_ALLOWED_METHODS = ["GET", "POST", "PUT", "DELETE", "PATCH"]

# CORS allowed headers
CORS_ALLOWED_HEADERS = ["Content-Type", "Authorization"]

# Security Headers Configuration
SECURITY_HEADERS = {
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "X-XSS-Protection": "1; mode=block",

}


JWT_EXPIRATION_TIME = 3


RESPONSE_MESSAGES = {
    "SUCCESS": "success",
    "ERROR": "error",
    "USER_ALREADY_EXIST": "user_already_exist",
    "USER_CREATED": "user_created",
    "WENT_WRONG": "went_wrong",
    "LOGIN_SUCCESS": "login_success",
    "VERIFIED_SUCCESSFULLY": "verified_successfully",
    "INVALID_CREDENTIALS": "invalid_credentials",
    "INVALID_OTP": "invalid_otp",
    "OTP_EXPIRED": "otp_expired",
    "USER_NOT_FOUND": "user_not_found",
    "NO_OTP": "no_otp",
    "RATE_LIMIT_EXCEEDED": "rate_limit_exceeded",
    "PASSWORD_CHANGED_SUCCESSFULLY": "password_changed_successfully",
    "VERIFICATION_CODE_SENT_SUCCESSFULLY": "verification_code_sent_successfully",
    "FAQ_NOT_FOUND": "faq_not_found",
    "FAQ_UPDATED_SUCCESSFULLY": "faq_updated_successfully",
    "TICKET_CREATED": "ticket_created",
    "TICKET_NOT_FOUND": "ticket_not_found",
    "CATEGORY_NOT_FOUND": "category_not_found",
    "CATEGORY_INACTIVE": "category_inactive",
    "INVALID_STATUS": "invalid_status",
    "CATEGORY_NOT_FOUND": "category_not_found",
    "TICKET_ALREADY_RESOLVED": "ticket_already_resolved",
    # Furniture store
    "FURNITURE_STORE_CREATED": "furniture_create_success",
    "FURNITURE_STORE_EXISTS": "furniture_store_exists",
    # Support ticket category
    "CATEGORY_EXISTS": "category_exists",
    "SUPPORT_TICKET_CATEGORY_CREATED_SUCCESS": "support_ticket_category_created_success",
}


AUTH_VERIFICATION_TYPE = {
    "FORGOT_PASSWORD": "forgot_password",
    "SIGNUP": "signup",
    "AGENT_SIGNUP": "agent_signup",
    "LOGIN": "login",
}


REDIS_KEYS = {
    "2FA_OTP": "2fa_otp:",
    "OTP": "otp:",
    "SIGNUP_INFO": "signup_info",
    "RESEND_OTP_COUNT": "resend_otp_count:",
}

# Time constants in seconds
TEN_MINUTES = 600
ONE_HOUR = 3600

REDIS_EXPIRY = {
    "2FA_OTP_EXPIRY": TEN_MINUTES,
    "OTP_EXPIRY": TEN_MINUTES,
    "SIGNUP_EXPIRY": ONE_HOUR,
    "RESEND_OTP_COOLDOWN": TEN_MINUTES,
}

# Rate limiting constants
RATE_LIMITS = {
    "RESEND_OTP_MAX_ATTEMPTS": 3,
}
