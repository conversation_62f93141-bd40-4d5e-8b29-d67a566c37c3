class SupportTicketRoutes:
    # Base route
    BASE = "/api/v1/support-tickets"

    # Support ticket routes
    CREATE = "/create-ticket"
    GET_ALL = "/get-all-tickets"
    GET_BY_ID = "/{ticket_id}"
    UPDATE_STATUS = "/{ticket_id}/status"

    # Support ticket category routes
    CREATE_CATEGORY = "/create-category"
    LIST_CATEGORIES = "/get-all-categories"  # New clean route
    GET_CATEGORY_BY_ID = "/get-category/{category_id}"
    UPDATE_CATEGORY = "/update-category/{category_id}"
    DELETE_CATEGORY = "/delete-category/{category_id}"


routes = SupportTicketRoutes()
