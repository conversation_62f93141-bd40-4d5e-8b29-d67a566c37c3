"""
Unified environment configuration system.

This module provides a centralized way to load environment variables
across different environments (local, development, uat, production).
"""

import os
from pathlib import Path
from typing import Any, Dict
from dotenv import dotenv_values


def get_env_variables() -> Dict[str, Any]:
    """
    Load environment variables based on the MODE environment variable.

    Returns:
        Dict[str, Any]: Dictionary containing environment variables

    Environment Files:
        - .env.local (default)
        - .env.development
        - .env.production
    """
    # Get the project root directory (3 levels up from this file)
    project_root = Path(__file__).parent.parent.parent.parent

    # Get mode from environment variable, default to "local"
    mode = os.getenv("MODE", "local").lower()

    # Map mode to environment file
    env_file_mapping = {
        "production": ".env.production",
        "development": ".env.development",
        "local": ".env.local",
    }

    # Get the appropriate env file name
    env_file_name = env_file_mapping.get(mode, ".env.local")

    # Construct the full path to the env file
    env_file_path = project_root / env_file_name

    # Load environment variables
    try:
        env_values = dotenv_values(env_file_path)
        if not env_values:
            print(
                f"Warning: No environment variables loaded from {env_file_path}")
        return env_values
    except Exception as e:
        print(f"Error loading environment file {env_file_path}: {e}")
        return {}


def get_env_value(key: str, default: Any | None = None) -> Any | None:
    """
    Get a specific environment variable value.

    Args:
        key (str): Environment variable key
        default (Any): Default value if key not found

    Returns:
        Any: Environment variable value or default
    """
    env_vars = get_env_variables()
    return env_vars.get(key, default)
