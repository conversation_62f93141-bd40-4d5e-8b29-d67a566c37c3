import { useDispatch } from "react-redux";
import { useNavigate, NavLink } from "react-router-dom";

import Logo from "../../assets/images/logo.png";
import CategoryIcon from "../svgElements/CategoryIcon";
import DashboardIcon from "../svgElements/DashboardIcon";
import EmailIcon from "../svgElements/EmailIcon";
import LogoutIcon from "../svgElements/LogoutIcon";
import RequestIcon from "../svgElements/RequestIcon";
import SettingsIcon from "../svgElements/SettingsIcon";
import SubscriptionIcon from "../svgElements/SubscriptionIcon";
import SupportIcon from "../svgElements/SupportIcon";
import TransactionIcon from "../svgElements/TransactionIcon";
import UserIcon from "../svgElements/UserIcon";
import { ROUTES } from "../../constants/routes";
import { authAction } from "../../redux/features/authSlice";
import "../../styles/sidebar.scss";
import { useState } from "react";
import LogoutModal from "../modals/LogoutModal";

const Sidebar = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showLogoutModal, setShowLogoutModal] = useState(false); 
  const handleLogout = () => {
    dispatch(authAction(null));
    navigate(ROUTES.LOGIN);
  };
  return (
    <div className="sidebar">
      <div>
        <div className="logo">
          <img src={Logo} alt="logo" />
        </div>
        <ul>
          <li>
            <NavLink to={ROUTES.DASHBOARD} className={({ isActive }) => (isActive ? "active" : "")}>
              <DashboardIcon />
              Dashboard
            </NavLink>
          </li>
          <li>
            <NavLink to={ROUTES.USERS_LIST} className={({ isActive }) => (isActive ? "active" : "")}>
              <UserIcon />
              User Management
            </NavLink>
          </li>
          <li>
            <NavLink to={ROUTES.CATEGORIES_CONFIGURATION} className={({ isActive }) => (isActive ? "active" : "")}>
              <CategoryIcon className="stroke-none" />
              Categories Config.
            </NavLink>
          </li>
          <li>
            <NavLink to={ROUTES.EMAIL_TEMPLATES} className={({ isActive }) => (isActive ? "active" : "")}>
              <EmailIcon />
              Email Templates
            </NavLink>
          </li>
          <li>
            <NavLink to={ROUTES.SUBSCRIPTION_PLANS} className={({ isActive }) => (isActive ? "active" : "")}>
              <SubscriptionIcon className="stroke-none" />
              Subscriptions Plans
            </NavLink>
          </li>
          <li>
            <NavLink to={ROUTES.TRANSACTIONS_HISTORY} className={({ isActive }) => (isActive ? "active" : "")}>
              <TransactionIcon className="stroke-none" />
              Transactions History
            </NavLink>
          </li>
          <li>
            <NavLink to={ROUTES.FURNITURE_STORE_REQUESTS} className={({ isActive }) => (isActive ? "active" : "")}>
              <RequestIcon className="stroke-none" />
              Furniture Store Req...
            </NavLink>
          </li>
          <li>
            <NavLink to={ROUTES.SUPPORT_TICKETS} className={({ isActive }) => (isActive ? "active" : "")}>
              <SupportIcon className="stroke-none" />
              Support Tickets
            </NavLink>
          </li>
          <li>
            <NavLink to={ROUTES.SETTINGS} className={({ isActive }) => (isActive ? "active" : "")}>
              <SettingsIcon />
              Settings
            </NavLink>
          </li>
        </ul>
      </div>
      <ul>
        <li>
          <a
           onClick={() => setShowLogoutModal(true)}
          >
            <LogoutIcon />
            Logout
          </a>
        </li>
      </ul>
      <LogoutModal isOpen={showLogoutModal} onClose={() => setShowLogoutModal(false)} onLogout={handleLogout} />
    </div>
  );
};

export default Sidebar;
