import logging
from typing import Any, Dict, Optional

import boto3

from app.core.aws.secret_manager import get_secret_keys
from app.core.config.config import get_env_variables

logger = logging.getLogger(__name__)

# Global variables for caching
_aws_session = None


def get_aws_session(region: Optional[str] = None) -> boto3.Session:
    """
    Create and return an authenticated AWS session based on environment settings.
    This centralizes the AWS authentication logic for the entire application.

    Args:
        region: Optional region override, will use environment settings if not provided

    Returns:
        boto3.Session: Authenticated AWS session
    """
    global _aws_session

    if _aws_session is None:
        env_vars = get_env_variables()
        mode = env_vars.get("MODE")
        region = region or env_vars.get("AWS_REGION") or "us-west-2"
        profile_name = env_vars.get("PROFILE_NAME")

        # Create a session with proper credentials
        if mode == "local" and profile_name:
            # Use AWS CLI profile credentials when in local mode
            _aws_session = boto3.Session(profile_name=profile_name, region_name=region)
            logger.info(f"Using AWS profile: {profile_name} in region: {region}")
        else:
            # Use instance profile or environment credentials
            _aws_session = boto3.Session(region_name=region)
            logger.info(f"Using instance profile or environment credentials in region: {region}")

    return _aws_session


def get_aws_client(service_name: str, region: Optional[str] = None) -> Any:
    """
    Get an AWS service client using the authenticated session.
    Use this function instead of boto3.client() directly throughout the application.

    Args:
        service_name: The AWS service to create a client for (e.g., 's3', 'dynamodb')
        region: Optional region override

    Returns:
        An authenticated AWS service client
    """
    session = get_aws_session(region)
    return session.client(service_name=service_name)


def get_db_credentials() -> Dict[str, str]:
    """
    Get PostgreSQL database connection credentials from secrets.

    Returns:
        Dict containing database connection parameters:
        - host: Database hostname
        - port: Database port
        - user: Database username
        - password: Database password
        - name: Database name
    """
    # First try to get credentials from AWS Secret Manager
    try:
        secrets = get_secret_keys()
    except Exception as e:
        logger.warning(f"Failed to get secrets from AWS Secret Manager: {e}")
        secrets = {}

    # Initialize with None values
    db_config: Dict[str, str] = {
        "host": None,
        "port": None,
        "user": None,
        "password": None,
        "name": None,
    }

    # Check if we have database credentials in the secrets
    if secrets and isinstance(secrets, dict):
        db_config["host"] = secrets.get("POSTGRES_SERVER")
        db_config["port"] = secrets.get("POSTGRES_PORT")
        db_config["user"] = secrets.get("POSTGRES_USER")
        db_config["password"] = secrets.get("POSTGRES_PASSWORD")
        db_config["name"] = secrets.get("POSTGRES_DB")

    return db_config


def get_aws_credentials() -> Dict[str, str]:
    """
    Get AWS credentials from secrets.

    Returns:
        Dict containing AWS credentials:
        - aws_access_key_id: AWS access key ID
        - aws_secret_access_key: AWS secret access key
        - aws_region: AWS region
        - aws_s3_bucket: AWS S3 bucket name
    """
    # First try to get credentials from AWS Secret Manager
    try:
        secrets = get_secret_keys()
    except Exception as e:
        logger.warning(f"Failed to get secrets from AWS Secret Manager: {e}")
        secrets = {}

    # Initialize with None values
    aws_creds: Dict[str, str] = {
        "aws_access_key_id": None,
        "aws_secret_access_key": None,
        "aws_region": None,
        "aws_s3_bucket": None,
    }

    # Check if we have AWS credentials in the secrets
    if secrets and isinstance(secrets, dict):
        aws_creds["aws_access_key_id"] = secrets.get("AWS_ACCESS_KEY_ID")
        aws_creds["aws_secret_access_key"] = secrets.get("AWS_SECRET_ACCESS_KEY")
        aws_creds["aws_region"] = secrets.get("AWS_REGION")
        aws_creds["aws_s3_bucket"] = secrets.get("AWS_S3_BUCKET")

    return aws_creds


def get_database_url() -> str:
    """
    Generate a database URL string for SQLAlchemy based on settings.

    Returns:
        str: Complete database connection URL
    """
    db_creds = get_db_credentials()

    # Validate that all required credentials are present
    required_fields = ["user", "password", "host", "port", "name"]
    missing_fields = [field for field in required_fields if not db_creds.get(field)]

    if missing_fields:
        logger.error(f"Missing required database credentials: {missing_fields}")
        raise ValueError(f"Incomplete database credentials. Missing: {missing_fields}")

    # Build the database URL
    database_url = f"postgresql+asyncpg://{db_creds['user']}:{db_creds['password']}@{db_creds['host']}:{db_creds['port']}/{db_creds['name']}"

    return database_url
