import React, { Suspense, lazy } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { ROUTES } from "../constants/routes";
import PrivateRoutes from "./PrivateRoutes";

// Auth Pages
const Login = lazy(() => import("../views/auth/Login"));
const ForgotPassword = lazy(() => import("../views/auth/ForgotPassword"));
const ResetPassword = lazy(() => import("../views/auth/ResetPassword"));
const Verification = lazy(() => import("../views/auth/Verification"));

// Protected Pages
const UsersList = lazy(() => import("../views/usersList/UsersList"));
const FurnitureStoreDetails = lazy(() => import("../views/furnitureStore/FurnitureStoreDetails"));
const AddNewFurnitureStore = lazy(() => import("../views/furnitureStore/AddNewFurnitureStore"));
const FurnitureStoreRequests = lazy(() => import("../views/furnitureStore/FurnitureStoreRequests"));
const FurnitureStoreRequestsDetails = lazy(() => import("../views/furnitureStore/FurnitureStoreRequestsDetails"));
const CategoriesConfiguration = lazy(() => import("../views/categories/CategoriesConfiguration"));
const SupportTickets = lazy(() => import("../views/supportTickets/SupportTickets"));

const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<>Loading...</>}>
      <Routes>
        {/* All routes are nested under PrivateRoutes wrapper */}
        <Route element={<PrivateRoutes />}>
          {/* Public Routes - Accessible without authentication */}
          <Route path={ROUTES.LOGIN} element={<Login />} />
          <Route path={ROUTES.FORGOT_PASSWORD} element={<ForgotPassword />} />
          <Route path={ROUTES.RESET_PASSWORD} element={<ResetPassword />} />
          <Route path={ROUTES.VERIFICATION} element={<Verification />} />

          {/* Private/Protected Routes - Require authentication */}
          <Route path={ROUTES.DASHBOARD} element={<div>Dashboard Coming Soon</div>} />
          <Route path={ROUTES.USERS_LIST} element={<UsersList />} />
          <Route path={ROUTES.FURNITURE_STORE_DETAILS} element={<FurnitureStoreDetails />} />
          <Route path={ROUTES.ADD_NEW_FURNITURE_STORE} element={<AddNewFurnitureStore />} />
          <Route path={ROUTES.FURNITURE_STORE_REQUESTS} element={<FurnitureStoreRequests />} />
          <Route path={ROUTES.FURNITURE_STORE_REQUESTS_DETAILS} element={<FurnitureStoreRequestsDetails />} />
          <Route path={ROUTES.CATEGORIES_CONFIGURATION} element={<CategoriesConfiguration />} />
          <Route path={ROUTES.FURNITURE_STORE_REQUESTS_DETAILS} element={<FurnitureStoreRequestsDetails />} />
          <Route path={ROUTES.CATEGORIES_CONFIGURATION} element={<CategoriesConfiguration />} />
          <Route path={ROUTES.SUPPORT_TICKETS} element={<SupportTickets />} />

          {/* Fallback route - redirect to login */}
          <Route path="*" element={<Navigate to={ROUTES.LOGIN} replace />} />
        </Route>
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
