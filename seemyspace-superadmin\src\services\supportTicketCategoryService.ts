import endpoints from "../constants/endpoints";
import type {
  ISupportTicketCategory,
  ICreateSupportTicketCategory,
  IUpdateSupportTicketCategory,
  ISupportTicketCategoryListResponse,
} from "../interfaces/categoryInterfaces";
import type { IApiResponseCommonInterface } from "../interfaces/commonInterfaces";
import * as http from "../utils/http";

/**
 * Support Ticket Category Service
 * Handles all API calls related to support ticket categories
 */
class SupportTicketCategoryService {
  /**
   * Create a new support ticket category
   * @param data - Category creation data
   * @returns Promise with API response
   */
  createCategory = async (
    data: ICreateSupportTicketCategory
  ): Promise<IApiResponseCommonInterface<ISupportTicketCategory>> => {
    return http.post(endpoints.supportTicketCategory.CREATE, data);
  };

  /**
   * Get all support ticket categories
   * @returns Promise with list of categories
   */
  listCategories = async (): Promise<
    IApiResponseCommonInterface<ISupportTicketCategoryListResponse[]>
  > => {
    return http.get(endpoints.supportTicketCategory.LIST);
  };

  /**
   * Get a specific category by ID
   * @param categoryId - Category ID to retrieve
   * @returns Promise with category data
   */
  getCategoryById = async (
    categoryId: number
  ): Promise<IApiResponseCommonInterface<ISupportTicketCategory>> => {
    return http.get(`${endpoints.supportTicketCategory.GET_BY_ID}/${categoryId}`);
  };

  /**
   * Update a support ticket category
   * @param categoryId - Category ID to update
   * @param data - Updated category data
   * @returns Promise with API response
   */
  updateCategory = async (
    categoryId: number,
    data: IUpdateSupportTicketCategory
  ): Promise<IApiResponseCommonInterface<ISupportTicketCategory>> => {
    return http.put(`${endpoints.supportTicketCategory.UPDATE}/${categoryId}`, data);
  };

  /**
   * Delete a support ticket category
   * @param categoryId - Category ID to delete
   * @returns Promise with API response
   */
  deleteCategory = async (
    categoryId: number
  ): Promise<IApiResponseCommonInterface<null>> => {
    return http.remove(`${endpoints.supportTicketCategory.DELETE}/${categoryId}`);
  };
}

export default new SupportTicketCategoryService();

