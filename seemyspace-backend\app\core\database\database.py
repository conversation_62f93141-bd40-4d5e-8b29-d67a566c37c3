import logging
from typing import AsyncGenerator, Generator

import sentry_sdk
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker
from app.core.aws.secret_manager import get_cached_secrets

# Import the secure credentials module
from app.core.aws.aws_config import get_database_url

# Load environment variables using unified config
from app.core.config.config import get_env_value

ENV = get_env_value("ENV", "development")

# Configure logging
logger = logging.getLogger(__name__)

secrets = get_cached_secrets()
logger.info(f"Secrets: {secrets}")
# Get database URL securely from secret_manager
DATABASE_URL = get_database_url()

# Create async database engine for RDS PostgreSQL with optimized settings
engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    pool_pre_ping=True,  # Verify connections before usage
    pool_size=20,  # Optimized pool size for better performance
    max_overflow=30,  # Optimized max overflow
    pool_recycle=1800,  # Recycle connections after 30 minutes
    pool_timeout=30,  # Increased timeout for better handling
    pool_reset_on_return="commit",  # Reset connection state on return
    isolation_level="READ_COMMITTED",  # Set isolation level
    future=True,  # Use SQLAlchemy 2.0 style
)

# Create sync database engine for sync use-cases (e.g., password reset)
# Convert async URL to sync URL by replacing asyncpg with psycopg2
sync_database_url = DATABASE_URL.replace("postgresql+asyncpg://", "postgresql+psycopg2://")
sync_engine = create_engine(
    sync_database_url,
    echo=False,
    pool_pre_ping=True,
    pool_size=10,  # Increased for sync operations
    max_overflow=20,  # Increased max overflow
    pool_recycle=1800,  # Recycle connections after 30 minutes
    pool_timeout=30,  # Increased timeout
    pool_reset_on_return="commit",  # Reset connection state on return
    isolation_level="READ_COMMITTED",  # Set isolation level
)

# Create async session factory with optimized settings
AsyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,  # Prevent expired object access issues
)

# Create sync session factory
SyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=sync_engine,
    class_=Session,
    expire_on_commit=False,  # Prevent expired object access issues
)

Base = declarative_base()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Get async database session for PostgreSQL RDS.

    Yields:
        AsyncGenerator: SQLAlchemy async database session.
    """
    async_session = AsyncSessionLocal()
    try:
        yield async_session
        # Don't auto-commit - let the calling function handle commits explicitly
    except Exception as e:
        sentry_sdk.capture_exception(e)
        try:
            await async_session.rollback()
        except Exception as rollback_error:
            logger.error(f"Error during rollback: {rollback_error}")
        logger.error(f"Database connection error: {e}", exc_info=True)
        raise
    finally:
        try:
            await async_session.close()
        except Exception as close_error:
            logger.error(f"Error closing database session: {close_error}")


# Dependency for synchronous DB session (for sync repository functions)
def get_sync_db() -> Generator[Session, None, None]:
    db = SyncSessionLocal()
    try:
        yield db
    finally:
        db.close()


# Database health check function
async def check_database_health() -> bool:
    """
    Check if the database connection is healthy.

    Returns:
        bool: True if database is healthy, False otherwise
    """
    try:
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT 1"))
            return result.scalar() == 1
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


# Connection pool monitoring
def get_pool_status():
    """
    Get current connection pool status for monitoring.

    Returns:
        dict: Pool status information
    """
    return {
        "async_pool_size": engine.pool.size(),
        "async_checked_in": engine.pool.checkedin(),
        "async_checked_out": engine.pool.checkedout(),
        "sync_pool_size": sync_engine.pool.size(),
        "sync_checked_in": sync_engine.pool.checkedin(),
        "sync_checked_out": sync_engine.pool.checkedout(),
    }
