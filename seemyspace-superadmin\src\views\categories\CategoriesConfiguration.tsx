import React, { useState, useEffect, useRef } from "react";

import Button from "../../components/formElements/Button";
import EditIcon from "../../components/svgElements/EditIcon";
import TrashIcon from "../../components/svgElements/TrashIcon";
import AddCategoryModal from "../../components/modals/AddCategoryModal";
import supportTicketCategoryService from "../../services/supportTicketCategoryService";
import { handleApiError } from "../../utils/http";
import { toastError, toastSuccess } from "../../utils/toast";
import type { ISupportTicketCategoryListResponse } from "../../interfaces/categoryInterfaces";
import "./categories.scss";
import Helper from "../../utils/helper";

interface Category {
  id: number;
  name: string;
}

interface ModalState {
  isOpen: boolean;
  mode: "add" | "edit";
  selectedCategory: ISupportTicketCategoryListResponse | null;
}

/**
 * Categories Configuration Component
 * Manages furniture and support ticket categories
 * Supports CRUD operations for support ticket categories
 */
const CategoriesConfiguration: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const initialFetchDone = useRef(false);

  // Modal state management
  const [modalState, setModalState] = useState<ModalState>({
    isOpen: false,
    mode: "add",
    selectedCategory: null,
  });

  // Categories state
  const [supportCategories, setSupportCategories] = useState<ISupportTicketCategoryListResponse[]>([]);

  // Furniture categories (static for now)
  const [furnitureCategories] = useState<Category[]>([
    { id: 1, name: "Chairs" },
    { id: 2, name: "Bedroom" },
    { id: 3, name: "Sofas" },
    { id: 4, name: "Tables" },
    { id: 5, name: "Storage" },
    { id: 6, name: "Office Furniture" },
    { id: 7, name: "Dining" },
    { id: 8, name: "Lighting" },
    { id: 9, name: "Outdoor Furniture" },
    { id: 10, name: "Decor" },
    { id: 11, name: "Others" },
  ]);

  /**
   * Fetch support ticket categories on component mount
   */
useEffect(() => {
  if (!initialFetchDone.current) {
    fetchSupportCategories();
    initialFetchDone.current = true;
  }
}, []);

  /**
   * Fetch all support ticket categories from API
   */
  const fetchSupportCategories = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const response = await supportTicketCategoryService.listCategories();

      if (response.success && response.data) {
        setSupportCategories(response.data);
      } else {
        toastError(response.message || "Failed to load categories");
      }
    } catch (error) {
      handleApiError(error, (key: string) => key, "CategoriesConfiguration - fetchSupportCategories");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle add new category button click
   */
  const handleAddNew = (): void => {
    setModalState({
      isOpen: true,
      mode: "add",
      selectedCategory: null,
    });
  };

  /**
   * Handle edit category button click
   */
  const handleEdit = (category: ISupportTicketCategoryListResponse): void => {
    setModalState({
      isOpen: true,
      mode: "edit",
      selectedCategory: category,
    });
  };

  /**
   * Handle delete category button click
   */
  const handleDelete = async (categoryId: number, categoryName: string): Promise<void> => {
    if (!window.confirm(`Are you sure you want to delete "${categoryName}"?`)) {
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await supportTicketCategoryService.deleteCategory(categoryId);

      if (response.success) {
        toastSuccess(response.message || "Category deleted successfully");
        await fetchSupportCategories();
      } else {
        toastError(response.message || "Failed to delete category");
      }
    } catch (error) {
      handleApiError(error, (key: string) => key, "CategoriesConfiguration - handleDelete");
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handle category form submission (add or edit)
   */
  const handleCategorySubmit = async (categoryName: string): Promise<void> => {
    try {
      setIsSubmitting(true);

      if (modalState.mode === "add") {
        // Create new category
        const response = await supportTicketCategoryService.createCategory({
          name: categoryName,
        });

        if (response.success) {
          toastSuccess(response.message || "Category added successfully");
          await fetchSupportCategories();
        } else {
          toastError(response.message || "Failed to add category");
        }
      } else if (modalState.mode === "edit" && modalState.selectedCategory) {
        // Update existing category
        const response = await supportTicketCategoryService.updateCategory(
          modalState.selectedCategory.id,
          { name: categoryName }
        );

        if (response.success) {
          toastSuccess(response.message || "Category updated successfully");
          await fetchSupportCategories();
        } else {
          toastError(response.message || "Failed to update category");
        }
      }
    } catch (error) {
      handleApiError(error, (key: string) => key, "CategoriesConfiguration - handleCategorySubmit");
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handle modal close
   */
  const handleCloseModal = (): void => {
    setModalState({
      isOpen: false,
      mode: "add",
      selectedCategory: null,
    });
  };

  const getModalTitle = (): string => {
    if (modalState.mode === "add") {
      return "Add New Support Ticket Category";
    }
    return "Edit Support Ticket Category";
  };

  return (
    <div className="categories-configuration">
      <h4 className="page-title">All Categories</h4>

      <div className="row">
        {/* Furniture Store Product Categories */}
        <div className="col-lg-6 col-md-12 mb-3">
          <div className="common-card">
            <div className="card-heading">
              <h4>Furniture Store Product Categories</h4>
              <Button className="primary-btn button-sm rounded-sm" disabled>
                Add New
              </Button>
            </div>
            <div className="card-body">
              <div className="category-list">
                {furnitureCategories.map((category) => (
                  <div key={category.id} className="category-item">
                    <span className="category-name">{Helper.toTitleCase(category?.name)}</span>
                    <div className="category-actions">
                      <button
                        className="action-btn"
                        disabled
                        aria-label="Edit category"
                        title="Coming soon"
                      >
                        <EditIcon />
                      </button>
                      <button
                        className="action-btn"
                        disabled
                        aria-label="Delete category"
                        title="Coming soon"
                      >
                        <TrashIcon />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Support Ticket Categories */}
        <div className="col-lg-6 col-md-12 mb-3">
          <div className="common-card">
            <div className="card-heading">
              <h4>Support Ticket Categories</h4>
              <Button
                className="primary-btn button-sm rounded-sm"
                onClick={handleAddNew}
                disabled={isSubmitting}
              >
                Add New
              </Button>
            </div>
            <div className="card-body">
              {isLoading ? (
                <div className="text-center py-4">
                  <p>Loading...</p>
                </div>
              ) : supportCategories.length === 0 ? (
                <div className="text-center py-4">
                  <p>No categories found</p>
                </div>
              ) : (
                <div className="category-list">
                  {supportCategories.map((category) => (
                    <div key={category.id} className="category-item">
                      <span className="category-name">{category.name}</span>
                      <div className="category-actions">
                        <button
                          className="action-btn"
                          onClick={() => handleEdit(category)}
                          aria-label="Edit category"
                          disabled={isSubmitting}
                          title="Edit category"
                        >
                          <EditIcon />
                        </button>
                        <button
                          className="action-btn"
                          onClick={() => handleDelete(category.id, category.name)}
                          aria-label="Delete category"
                          disabled={isSubmitting}
                          title="Delete category"
                        >
                          <TrashIcon />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add/Edit Category Modal */}
      <AddCategoryModal
        isOpen={modalState.isOpen}
        onClose={handleCloseModal}
        onSubmit={handleCategorySubmit}
        title={getModalTitle()}
        mode={modalState.mode}
        initialData={
          modalState.selectedCategory
            ? {
                id: modalState.selectedCategory.id,
                name: modalState.selectedCategory.name,
              }
            : undefined
        }
        isSubmitting={isSubmitting}
      />
    </div>
  );
};

export default CategoriesConfiguration;
