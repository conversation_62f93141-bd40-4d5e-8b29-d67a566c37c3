import * as yup from "yup";
import { CATEGORY_NAME_REGEX } from "../constants/validationConstant";
import { VALIDATION_MESSAGES, VALIDATION_RULES } from "../constants/commonConstant";

/**
 * Category creation validation schema
 * Validates all required fields for creating a new category
 */
export const createCategoryValidationSchema = yup.object().shape({
  categoryName: yup
    .string()
    .trim()
    .required(VALIDATION_MESSAGES.CATEGORY_NAME_REQUIRED)
    .matches(CATEGORY_NAME_REGEX, VALIDATION_MESSAGES.CATEGORY_NAME_INVALID)
    .min(VALIDATION_RULES.CATEGORY_NAME_MIN_LENGTH, VALIDATION_MESSAGES.CATEGORY_NAME_MIN)
    .max(VALIDATION_RULES.CATEGORY_NAME_MAX_LENGTH, VALIDATION_MESSAGES.CATEGORY_NAME_MAX),
});
