from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from sqlalchemy import select ,func
from fastapi import status ,HTTPException
from app.core.logging_config import get_logger, setup_logging
from app.utils.auth_utils import hash_password, verify_password
from app.core.response import ResponseModal
from app.models.user import User
from app.models import UserStatus
from app.models.furniture_store import FurnitureStore
from app.models.furniture_category import FurnitureCategory
from typing import List
from app.utils.auth_utils import hash_password
from app.utils.constants import RESPONSE_MESSAGES

setup_logging(level="DEBUG")
logger = get_logger(__name__)


class FurnitureStoreRepository:
    
    async def _generate_store_id(self, db) -> str:
        """
        Generate a new store_id like SMS001, SMS002, etc.
        """
        result = await db.execute(select(func.count(FurnitureStore.id)))
        count = result.scalar() or 0
        new_number = count + 1
        return f"SMS{new_number:03d}"
    

    async def create_furniture_store(self, db, store_data) -> ResponseModal:
        """
        Create a new Furniture House Admin user and associated Furniture Store.
        """
        try:
            # ✅ Check if user with same email exists
            existing_user = await db.execute(
                select(User).where(User.email == store_data.email)
            )
            existing_user = existing_user.scalar_one_or_none()

            if existing_user:
                return ResponseModal(
                    success=False,
                    message=RESPONSE_MESSAGES["FURNITURE_STORE_EXISTS"],
                    status_code=status.HTTP_400_BAD_REQUEST,
                )

            # ✅ Create user
            new_user = User(
                name=store_data.name,
                email=store_data.email,
                password=hash_password(store_data.password),
                account_type=store_data.account_type.upper(),
                status=UserStatus.ACTIVE,
            )

            db.add(new_user)
            await db.flush()  # Get new_user.id

            # ✅ Generate store_id like SMS001
            store_id = await self._generate_store_id(db)

            # ✅ Create furniture store
            new_store = FurnitureStore(
                store_id=store_id,
                user_id=new_user.id,
                contact_person_name=store_data.contact_person_name,
                contact_person_email=store_data.contact_person_email,
                contact_person_phone=store_data.contact_person_phone,
                country_code=getattr(store_data, "country_code", 1),  # default to 1
            )

            db.add(new_store)
            await db.commit()
            await db.refresh(new_store)

            # Mailgun email send code 

            logger.info(f"✅ Furniture store created successfully: {new_store.store_id}")

            return ResponseModal(
                success=True,
                message=RESPONSE_MESSAGES ["FURNITURE_STORE_CREATED"],
                status_code=status.HTTP_201_CREATED
            )

        except Exception as e:
            await db.rollback()
            logger.error(f"[FurnitureStore] Error creating store: {e}", exc_info=True)
            return ResponseModal(
                success=False,
                message=RESPONSE_MESSAGES["WENT_WRONG"],
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    async def update_furniture_store(self, db: AsyncSession, user_id: int, update_data) -> ResponseModal:
        """
        Update furniture store and associated user details using user_id (async-safe).
        """
        try:
            # 1️⃣ Fetch User (main table)
            user = await db.get(User, user_id)
            if not user:
                return ResponseModal(
                    success=False,
                    message="User not found.",
                    status_code=status.HTTP_404_NOT_FOUND,
                )

            # 2️⃣ Fetch FurnitureStore explicitly (avoid lazy loading)
            result = await db.execute(
                select(FurnitureStore).where(FurnitureStore.user_id == user_id)
            )
            store = result.scalar_one_or_none()

            if not store:
                return ResponseModal(
                    success=False,
                    message="No furniture store details found for this user.",
                    status_code=status.HTTP_404_NOT_FOUND,
                )

            # 3️⃣ Update User fields
            user.name = update_data.name or user.name
            user.email = update_data.email or user.email
            user.phone_number=update_data.phone or user.phone_number
            user.account_type = update_data.account_type.upper() if update_data.account_type else user.account_type
            user.profile_image = update_data.profile_image or user.profile_image
            user.updated_at = datetime.utcnow()
            print("USER DETAIS",user)
            # 4️⃣ Update FurnitureStore fields
            store.contact_person_name = update_data.contact_person_name or store.contact_person_name
            store.contact_person_email = update_data.contact_person_email or store.contact_person_email
            store.contact_person_phone = update_data.contact_person_phone or store.contact_person_phone
            store.updated_at = datetime.utcnow()

            await db.commit()
            await db.refresh(user)
            await db.refresh(store)

            logger.info(f"✅ Furniture store for user_id={user_id} updated successfully.")

            return ResponseModal(
                success=True,
                message="Furniture store updated successfully.",
                status_code=status.HTTP_200_OK,
                data={
                    "user_id": user.id,
                    "store_id": store.store_id,
                    "store_name": user.name,
                    "email": user.email,
                    "account_type": user.account_type,
                    "profile_image": user.profile_image,
                    "contact_person": {
                        "name": store.contact_person_name,
                        "email": store.contact_person_email,
                        "phone": store.contact_person_phone,
                    },
                    "updated_at": store.updated_at.isoformat(),
                },
            )

        except Exception as e:
            await db.rollback()
            logger.error(f"[FurnitureStore] Error updating store for user_id={user_id}: {e}", exc_info=True)
            return ResponseModal(
                success=False,
                message=f"Error updating furniture store: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    async def get_furniture_store_detail(self, db: AsyncSession, store_id: int) -> ResponseModal:
            """
            Get furniture store details by ID (joined User + FurnitureStore tables).
            """
            try:
                # ✅ Fetch store joined with user
                result = await db.execute(
                    select(FurnitureStore)
                    .options(joinedload(FurnitureStore.user))
                    .where(FurnitureStore.id == store_id)
                )
                store = result.scalar_one_or_none()

                if not store:
                    return ResponseModal(
                        success=False,
                        message=f"Furniture store with ID {store_id} not found.",
                        status_code=status.HTTP_404_NOT_FOUND,
                    )

                # ✅ Extract user (Furniture House Admin)
                user = store.user

                # ✅ Structure response same as creation API
                store_data = {
                    "store_id": store.store_id,
                    "user_id": store.user_id,
                    "store_name": user.name,
                    "email": user.email,
                    "account_type": user.account_type,
                    "status": user.status,
                    "contact_person": {
                        "name": store.contact_person_name,
                        "email": store.contact_person_email,
                        "phone": store.contact_person_phone,
                    },
                    "country_code": store.country_code,
                    "is_active": getattr(user, "is_active", True),
                    "created_at": store.created_at.isoformat() if store.created_at else None,
                    "updated_at": store.updated_at.isoformat() if store.updated_at else None,
                }

                logger.info(f"✅ Retrieved furniture store details for: {store.store_id}")

                return ResponseModal(
                    success=True,
                    message="Furniture store details retrieved successfully.",
                    status_code=status.HTTP_200_OK,
                    data=store_data,
                )

            except Exception as e:
                await db.rollback()
                logger.error(f"[FurnitureStore] Error fetching store details: {e}", exc_info=True)
                return ResponseModal(
                    success=False,
                    message=f"Error fetching furniture store details: {str(e)}",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

    async def change_password(self, db: AsyncSession, user_id: int, password_data) -> ResponseModal:
            """
            Change password for a furniture store user after verifying old password.
            """
            try:
                # ✅ Fetch user
                user = await db.get(User, user_id)
                if not user:
                    return ResponseModal(
                    success=False,
                    message="User not found.",
                    status_code=status.HTTP_404_NOT_FOUND,
                    )

                # ✅ Verify old password
                if not verify_password(password_data.old_password, user.password):
                        return ResponseModal(
                            success=False,
                            message="Incorrect old password.",
                            status_code=status.HTTP_400_BAD_REQUEST,
                        )

                 # ✅ Update to new password
                user.password = hash_password(password_data.new_password)
                user.updated_at = datetime.utcnow()
                await db.commit()

                logger.info(f"✅ Password changed successfully for user_id={user_id}")
                return ResponseModal(
                        success=True,
                        message="Password changed successfully.",
                        status_code=status.HTTP_200_OK,
                    )    

            except Exception as e:
                    await db.rollback()
                    logger.error(f"[FurnitureStore] Error changing password: {e}", exc_info=True)
                    return ResponseModal(
                        success=False,
                        message=f"Error changing password: {str(e)}",
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )


    async def get_all_categories(self, db: AsyncSession) -> ResponseModal:
        """Fetch all active furniture product categories."""
        try:
            logger.info("Fetching all active product categories...")
            query = (
            select(FurnitureCategory)
            .where(FurnitureCategory.is_active.is_(True))
            .order_by(FurnitureCategory.name)
            )

            result = await db.execute(query)
            categories = result.scalars().all()
            logger.info(f"Found {len(categories)} active categories")

            categories_data = [
                    {
                        "id": category.id,
                        "name": category.name,
                        "is_active": category.is_active,
                        "created_at": category.created_at.isoformat() if category.created_at else None,
                        "updated_at": category.updated_at.isoformat() if category.updated_at else None,
                    }
                    for category in categories
                ]

            return ResponseModal(
                    success=True,
                    message="Active product categories retrieved successfully.",
                    status_code=status.HTTP_200_OK,
                    data={"categories": categories_data},
                )

        except Exception as e:
            logger.error(f"[ProductCategory] Fetch error: {e}", exc_info=True)
            return ResponseModal(
                success=False,
                message=RESPONSE_MESSAGES.get("Went_Wrong", "An error occurred while fetching product categories."),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            ) 
     
    async def create_product_category(self, db: AsyncSession, name: str) -> ResponseModal:
        """Create a new furniture product category if not duplicate."""
        try:
            # Duplicate active name check
            result = await db.execute(
                select(FurnitureCategory).where(
                    FurnitureCategory.name == name,
                    FurnitureCategory.is_active == True,
                )
            )
            if result.scalar_one_or_none():
                return ResponseModal(
                    success=False,
                    message="Category name already exists.",
                    status_code=status.HTTP_409_CONFLICT,
                )

            category = FurnitureCategory(
                name=name.strip(),
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            db.add(category)
            await db.commit()
            await db.refresh(category)

            return ResponseModal(
                success=True,
                message="Product category created successfully.",
                status_code=status.HTTP_201_CREATED,
                data={"id": category.id, "name": category.name},
            )
        except Exception as e:
            await db.rollback()
            logger.error(f"[ProductCategory] Create error: {e}")
            return ResponseModal(
                success=False,
                message=RESPONSE_MESSAGES.get("Went_Wrong", "Something went wrong."),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    async def update_product_category(self, db: AsyncSession, category_id: int, name: str) -> ResponseModal:
        """Update an existing furniture product category name with duplicate check."""
        try:
            category = await db.get(FurnitureCategory, category_id)
            if not category or not category.is_active:
                return ResponseModal(
                    success=False,
                    message="Product category not found.",
                    status_code=status.HTTP_404_NOT_FOUND,
                )

            # Check duplicate against other active categories
            result = await db.execute(
                select(FurnitureCategory).where(
                    FurnitureCategory.name == name,
                    FurnitureCategory.is_active == True,
                    FurnitureCategory.id != category_id,
                )
            )
            if result.scalar_one_or_none():
                return ResponseModal(
                    success=False,
                    message="Category name already exists.",
                    status_code=status.HTTP_409_CONFLICT,
                )

            category.name = name.strip()
            category.updated_at = datetime.utcnow()
            await db.commit()

            return ResponseModal(
                success=True,
                message="Product category updated successfully.",
                status_code=status.HTTP_200_OK,
                data={"id": category.id, "name": category.name},
            )
        except Exception as e:
            await db.rollback()
            logger.error(f"[ProductCategory] Update error: {e}")
            return ResponseModal(
                success=False,
                message=RESPONSE_MESSAGES.get("Went_Wrong", "Something went wrong."),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    async def delete_product_category(self, db: AsyncSession, category_id: int) -> ResponseModal:
        """Soft delete a product category by marking it inactive."""
        try:
            category = await db.get(FurnitureCategory, category_id)
            if not category:
                return ResponseModal(
                    success=False,
                    message="Product category not found.",
                    status_code=status.HTTP_404_NOT_FOUND,
                )

            category.is_active = False
            category.updated_at = datetime.utcnow()
            await db.commit()

            return ResponseModal(
                success=True,
                message="Product category deleted successfully.",
                status_code=status.HTTP_200_OK,
            )
        except Exception as e:
            await db.rollback()
            logger.error(f"[ProductCategory] Delete error: {e}")
            return ResponseModal(
                success=False,
                message=RESPONSE_MESSAGES.get("Went_Wrong", "Something went wrong."),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
