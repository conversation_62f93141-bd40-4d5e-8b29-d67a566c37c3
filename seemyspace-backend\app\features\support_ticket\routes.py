from fastapi import APIRouter, Depends, status, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.database import get_async_db
from app.core.response import ResponseModal
from app.features.support_ticket.repository import SupportTicketRepository
from app.features.support_ticket.route_definitions import routes
from app.features.support_ticket.schemas import CreateSupportTicket, SupportTicketFilterParams, SupportTicketCategoryResponse
from app.middleware.verify import roles_required
from app.models.user import AccountType


router = APIRouter(prefix=routes.BASE, tags=["Support Tickets"])
support_ticket_repository = SupportTicketRepository()


@router.post(
    routes.CREATE,
    response_model=ResponseModal,
    status_code=status.HTTP_201_CREATED,
)
async def create_support_ticket(
    ticket: CreateSupportTicket,
    db: AsyncSession = Depends(get_async_db),
    user: dict = Depends(roles_required(
        [AccountType.AGENT, AccountType.BUYER]))
) -> ResponseModal:
    """Create a new support ticket.

    Args:
        ticket (CreateSupportTicket): Support ticket data including category, subject, message, and optional images
        db (AsyncSession): Async database session dependency

    Returns:
        ResponseModal: Response containing success status and created ticket data with ticket_id
    """
    return await support_ticket_repository.create_support_ticket(db, ticket, user)


@router.get(
    routes.GET_ALL,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def get_all_support_tickets(
    filters: SupportTicketFilterParams = Depends(),
    db: AsyncSession = Depends(get_async_db)
) -> ResponseModal:
    """Get all support tickets with comprehensive filtering, searching, and pagination.

    Args:
        filters: Filter and pagination parameters
        db: Database session

    Returns:
        ResponseModal with list of support tickets
    """
    return await support_ticket_repository.get_all_support_tickets(db=db, filters=filters)



@router.get(
    routes.LIST_CATEGORIES,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def list_support_ticket_categories(
    db: AsyncSession = Depends(get_async_db),
) -> ResponseModal:
    """
    List all active support ticket categories.

    Returns:
        ResponseModal: List of active categories
    """

    return await support_ticket_repository.list_categories(db)


@router.get(
    routes.GET_CATEGORY_BY_ID,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def get_support_ticket_category_by_id(
    category_id: int = Path(..., gt=0,
                            description="Category ID to retrieve (must be positive)"),
    db: AsyncSession = Depends(get_async_db),

) -> ResponseModal:
    """
    Get a specific support ticket category by ID.

    Args:
        category_id (int): ID of the category to retrieve (must be positive)
        db (AsyncSession): Async database session dependency
        user (dict): Authenticated admin user from JWT token

    Returns:
        ResponseModal: Response containing support ticket category data or error message
    """
    return await support_ticket_repository.get_category_by_id(db, category_id)

@router.post(
    routes.CREATE_CATEGORY,
    response_model=ResponseModal,
    status_code=status.HTTP_201_CREATED,
)
async def create_support_ticket_category(
    category: SupportTicketCategoryResponse,
    db: AsyncSession = Depends(get_async_db),
    user: dict = Depends(roles_required([AccountType.ADMIN]))
) -> ResponseModal:
    """
    Create a new support ticket category.

    Args:
        category (SupportTicketCategoryResponse): Support ticket category data including name
        db (AsyncSession): Async database session dependency
        user (dict): Authenticated admin user from JWT token

    Returns:
        ResponseModal: Response containing success status and created category data
    """
    return await support_ticket_repository.create_category(db, name=category.name)



@router.put(
    routes.UPDATE_CATEGORY,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def update_support_ticket_category(
    category_id: int = Path(..., gt=0,
                            description="Category ID to update (must be positive)"),
    category_data: SupportTicketCategoryResponse = None,
    db: AsyncSession = Depends(get_async_db),
    user: dict = Depends(roles_required([AccountType.ADMIN]))
) -> ResponseModal:
    """
    Update a support ticket category name.

    Args:
        category_id (int): ID of the support ticket category to update (must be positive)
        category_data (SupportTicketCategoryResponse): Updated category data including name
        db (AsyncSession): Async database session dependency
        user (dict): Authenticated admin user from JWT token

    Returns:
        ResponseModal: Response containing success status and updated category data
    """
    return await support_ticket_repository.update_support_ticket_category(
        db, category_id, name=category_data.name
    )


@router.delete(
    routes.DELETE_CATEGORY,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def delete_support_ticket_category(
    category_id: int = Path(..., gt=0,
                            description="Category ID to delete (must be positive)"),
    db: AsyncSession = Depends(get_async_db),
    user: dict = Depends(roles_required([AccountType.ADMIN]))
) -> ResponseModal:
    """
    Delete a support ticket category by ID (soft delete).

    Args:
        category_id (int): ID of the support ticket category to delete (must be positive)
        db (AsyncSession): Async database session dependency
        user (dict): Authenticated admin user from JWT token

    Returns:
        ResponseModal: Response indicating success or failure of deletion
    """
    return await support_ticket_repository.delete_support_ticket_category(db, category_id)


@router.get(
    routes.GET_BY_ID,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def get_support_ticket_by_id(
    ticket_id: int = Path(..., gt=0,
                          description="Support ticket ID to retrieve (must be positive)"),
    db: AsyncSession = Depends(get_async_db),
    user: dict = Depends(roles_required([AccountType.ADMIN]))
) -> ResponseModal:
    """Get a specific support ticket by ID.

    Args:
        ticket_id: Support ticket ID to retrieve (must be positive)
        db: Async database session dependency
        user: Authenticated admin user

    Returns:
        ResponseModal: Response containing support ticket data
    """
    return await support_ticket_repository.get_support_ticket_by_id(db, ticket_id)


@router.put(
    routes.UPDATE_STATUS,
    response_model=ResponseModal,
    status_code=status.HTTP_200_OK,
)
async def update_ticket_status(
    ticket_id: int = Path(..., gt=0,
                          description="Support ticket ID to update (must be positive)"),
    db: AsyncSession = Depends(get_async_db),
    user: dict = Depends(roles_required([AccountType.ADMIN]))
) -> ResponseModal:
    """Update the status of a support ticket.


    Once a ticket is marked as resolved, it cannot be updated again.

    Args:
        ticket_id: ID of the ticket to update (must be positive)
        status_data: New status data (pending or resolved)
        db: Database session
        user: Authenticated admin user

    Returns:
        ResponseModal with success status
    """
    return await support_ticket_repository.update_ticket_status(db, ticket_id)
