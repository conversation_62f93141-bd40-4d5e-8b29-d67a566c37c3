import re
from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional
from datetime import datetime

class CreateFurnitureStore(BaseModel):
    """
    Schema for creating a Furniture Store and its associated user (Furniture House Admin).
    """
    name: str = Field(..., min_length=1, max_length=75)
    email: EmailStr
    phone: str = Field(..., min_length=10, max_length=20)
    password: str = Field(..., min_length=8, max_length=255)
    account_type: str = Field(..., description="Account type")
    country_code: int = Field(default=1, description="Country code (default: 1 for USA)") # default to USA
    contact_person_name: str = Field(..., max_length=100)
    contact_person_email: EmailStr
    contact_person_phone: str = Field(..., min_length=10, max_length=20)
    profile_image: Optional[str] = None

    @field_validator("password")
    @classmethod
    def strong_password(cls, v):
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters")
        if " " in v:
            raise ValueError("Password cannot contain spaces")
        if not re.search(r"[A-Z]", v):
            raise ValueError("Password must include an uppercase letter")
        if not re.search(r"[a-z]", v):
            raise ValueError("Password must include a lowercase letter")
        if not re.search(r"[0-9]", v):
            raise ValueError("Password must include a number")
        if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", v):
            raise ValueError("Password must include a special character")
        return v

    @field_validator("phone", "contact_person_phone")
    @classmethod
    def validate_phone(cls, v):
        # cleaned = v.replace(" ", "")
        if not re.match(r"^\+1 \(\d{3}\) \d{3}-\d{4}$", v): # USA phone number format
            raise ValueError("Invalid phone number format")
        return v

class UpdateFurnitureStore(BaseModel):
    """
    Schema for updating Furniture Store and its associated user details.
    All fields are optional to allow partial updates.
    """
    name: Optional[str] = Field(None, max_length=75)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, min_length=10, max_length=20)
    contact_person_phone: Optional[str] = Field(None, min_length=10, max_length=20)
    account_type: Optional[str] = Field(None, description="Account type")
    country_code: Optional[int] = Field(None, description="Country code")
    contact_person_name: Optional[str] = Field(None, max_length=100)
    contact_person_email: Optional[EmailStr] = None
    # contact_person_phone: Optional[str] = Field(None, min_length=10, max_length=15)
    profile_image: Optional[str] = None

    @field_validator("phone", "contact_person_phone")
    @classmethod
    def validate_phone(cls, v):
        if v is None:
            return v
        # Accept +**************** or (415)555-1234 or +1********** or **********
        pattern = r"^(\+1\s?)?\(?\d{3}\)?[\s-]?\d{3}[\s-]?\d{4}$"
        if not re.match(pattern, v):
            raise ValueError("Invalid phone number format. Examples: +**************** or **********")
        return v

# Furniture Product Category Schemas
class ProductCategoryCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)


class ProductCategoryUpdate(BaseModel):
    id: int
    name: str = Field(..., min_length=1, max_length=100)

class FurnitureStoreDetailResponse(BaseModel):
    """
    Schema for returning furniture store details with user information.
    """
    id: int
    store_id: str
    store_name: str
    email: EmailStr
    account_type: str
    contact_person_name: str
    contact_person_email: EmailStr
    contact_person_phone: str
    country_code: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class FurnitureStoreQuery(BaseModel):
    """
    Schema for querying a specific furniture store by ID.
    """
    store_id: int = Field(..., description="Furniture Store ID")


class ChangePasswordRequest(BaseModel):
    """
    Schema for changing password of a furniture store user.
    """
    old_password: str = Field(..., min_length=8, max_length=255)
    new_password: str = Field(..., min_length=8, max_length=255)

    @field_validator("new_password")
    @classmethod
    def strong_password(cls, v):
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters")
        if " " in v:
            raise ValueError("Password cannot contain spaces")
        if not re.search(r"[A-Z]", v):
            raise ValueError("Password must include an uppercase letter")
        if not re.search(r"[a-z]", v):
            raise ValueError("Password must include a lowercase letter")
        if not re.search(r"[0-9]", v):
            raise ValueError("Password must include a number")
        if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", v):
            raise ValueError("Password must include a special character")
        return v