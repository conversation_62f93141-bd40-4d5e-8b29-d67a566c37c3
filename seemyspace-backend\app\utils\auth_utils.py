from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, Any
import jwt

from passlib.context import Crypt<PERSON>ontext

from app.utils import constants
from app.core.aws.secret_manager import get_cached_secrets
import logging

logger = logging.getLogger(__name__)

# JWT Configuration from your existing config system
secrets = get_cached_secrets()
JWT_SECRET_KEY = secrets.get("JWT_SECRET_KEY")
JWT_ALGORITHM = secrets.get("JWT_ALGORITHM")

logger.info(f"secrets: {secrets}")
logger.info(f"JWT_ALGORITHM: {JWT_ALGORITHM}")

# Create password context with bcrypt hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def hash_password(password: str) -> str:
    """
    Hash a password using bcrypt.

    Args:
        password (str): Plain text password to hash

    Returns:
        str: Hashed password
    """
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash.

    Args:
        plain_password (str): Plain text password to verify
        hashed_password (str): Hashed password to verify against

    Returns:
        bool: True if password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)


def create_jwt_token(user_data: Dict[str, Any]) -> str:
    """
    Create a JWT token for user authentication.

    Args:
        user_data (Dict[str, Any]): User data to include in token

    Returns:
        str: JWT token

    Raises:
        KeyError: If required user data fields are missing
        ValueError: If JWT configuration is invalid or token creation fails
    """
    try:

        # Set expiration time
        expire = datetime.utcnow() + timedelta(days=constants.JWT_EXPIRATION_TIME)

        # Create payload
        payload = {
            "user_id": user_data["id"],
            "email": user_data["email"],
            "account_type": user_data["account_type"],
            "exp": expire,
            "iat": datetime.utcnow()
        }

        # Create token
        token = jwt.encode(
            payload,
            JWT_SECRET_KEY,
            algorithm=JWT_ALGORITHM
        )

        return token

    except KeyError as e:
        logger.error(f"Invalid user data: {e}")
        raise KeyError("Invalid user data")
    except Exception as e:
        logger.error(f"Failed to create JWT token: {e}")
        raise ValueError("Failed to create JWT token")
