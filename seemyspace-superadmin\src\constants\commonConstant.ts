export const customError = {
  something_went_wrong: "went_wrong",
  session_expired: "session_expired",
};

export const slicesTypes = {
  authSlice: "auth",
};

/**
 * Account types for admin login
 */
export const ACCOUNT_TYPE = {
  ADMIN: "admin",
};

/**
 * Verification types for OTP verification
 */
export const VERIFICATION_TYPE = {
  FORGOT_PASSWORD: "forgot_password",
  LOGIN: "login",
} as const;

export type VerificationType = (typeof VERIFICATION_TYPE)[keyof typeof VERIFICATION_TYPE];

export const FORMAT_NUMBER_USA = "+1 (###) ###-####";

export const VALIDATION_RULES = {
  STORE_NAME_MIN_LENGTH: 3,
  STORE_NAME_MAX_LENGTH: 100,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  CATEGORY_NAME_MIN_LENGTH: 3,
  CATEGORY_NAME_MAX_LENGTH: 50,
};

export const VALIDATION_MESSAGES = {
  // Furniture store validation messages
  STORE_NAME_REQUIRED: "Store name is required",
  STORE_NAME_MIN: `Store name must be at least ${VALIDATION_RULES.STORE_NAME_MIN_LENGTH} characters`,
  STORE_NAME_MAX: `Store name cannot exceed ${VALIDATION_RULES.STORE_NAME_MAX_LENGTH} characters`,
  STORE_NAME_INVALID: "Store name can only contain letters, numbers, spaces, &, apostrophes and hyphens",
  EMAIL_REQUIRED: "Email is required",
  EMAIL_INVALID: "Please enter a valid email address",
  PHONE_REQUIRED: "Phone number is required",
  PHONE_INVALID: "Please enter a valid phone number",
  CONTACT_NAME_REQUIRED: "Contact person name is required",
  CONTACT_NAME_MIN: `Contact person name must be at least ${VALIDATION_RULES.NAME_MIN_LENGTH} characters`,
  CONTACT_NAME_MAX: `Contact person name cannot exceed ${VALIDATION_RULES.NAME_MAX_LENGTH} characters`,
  CONTACT_NAME_INVALID: "Contact person name can only contain letters, spaces, apostrophes and hyphens",
  CONTACT_EMAIL_REQUIRED: "Contact person email is required",
  CONTACT_PHONE_REQUIRED: "Contact person phone is required",
  PASSWORD_REQUIRED: "Password is required",
  PASSWORD_INVALID: "Password must contain at least 8 characters, one uppercase, one lowercase, one number and one special character",
  CONFIRM_PASSWORD_REQUIRED: "Confirm password is required",
  PASSWORD_NOT_MATCH: "Passwords do not match",

  // Category validation messages
  CATEGORY_NAME_REQUIRED: "Category name is required",
  CATEGORY_NAME_MIN: `Category name must be at least ${VALIDATION_RULES.CATEGORY_NAME_MIN_LENGTH} characters`,
  CATEGORY_NAME_MAX: `Category name must not exceed ${VALIDATION_RULES.CATEGORY_NAME_MAX_LENGTH} characters`,
  CATEGORY_NAME_INVALID: "Category name can only contain letters, spaces, and hyphens",

  // Something Went Wrong
  SOMETHING_WENT_WRONG: "Something went wrong. Please try again later",
};

export enum AccountType {
  FURNITURE_HOUSE_ADMIN = "furniture_house_admin",
}
