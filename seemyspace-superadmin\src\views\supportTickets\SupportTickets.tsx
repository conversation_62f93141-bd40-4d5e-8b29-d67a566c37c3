import { useState } from "react";
import EyeIcon from "../../components/svgElements/EyeIcon";
import HeaderSearch from "../../components/common/HeaderSearch";
import Button from "../../components/formElements/Button";
import Select from "../../components/formElements/Select";
import { useForm } from "react-hook-form";
import "./SupportTickets.scss";

interface Ticket {
  ticketId: string;
  userType: string;
  name: string;
  email: string;
  category: string;
  subject: string;
  dateCreated: string;
  status: "Pending" | "Resolved";
}

const SupportTickets = () => {
  const { control } = useForm();
  const [tickets] = useState<Ticket[]>([
    {
      ticketId: "SP001",
      userType: "Buyer",
      name: "<PERSON>",
      email: "<EMAIL>",
      category: "Login Issue",
      subject: "Unable to Sign In",
      dateCreated: "Oct 25, 2025",
      status: "Pending",
    },
    {
      ticketId: "SP002",
      userType: "Furniture Store",
      name: "<PERSON>",
      email: "<EMAIL>",
      category: "Payment Support",
      subject: "Payment not received",
      dateCreated: "Oct 22, 2025",
      status: "Resolved",
    },
    {
      ticketId: "SP003",
      userType: "Agent",
      name: "Emily Parker",
      email: "<EMAIL>",
      category: "App Bug",
      subject: "App freezes on upload",
      dateCreated: "Oct 21, 2025",
      status: "Pending",
    },
    {
      ticketId: "SP004",
      userType: "N/A",
      name: "Johnathan Smith",
      email: "<EMAIL>",
      category: "Account Update",
      subject: "Need to change phone number",
      dateCreated: "Oct 20, 2025",
      status: "Resolved",
    },
    {
      ticketId: "SP005",
      userType: "Furniture Store",
      name: "Sarah Lee",
      email: "<EMAIL>",
      category: "Feedback",
      subject: "Product listing improvement",
      dateCreated: "Oct 15, 2025",
      status: "Pending",
    },
    {
      ticketId: "SP006",
      userType: "Agent",
      name: "Michael Green",
      email: "<EMAIL>",
      category: "Feature Request",
      subject: "Add filter for locations",
      dateCreated: "Oct 12, 2025",
      status: "Resolved",
    },
    {
      ticketId: "SP007",
      userType: "Buyer",
      name: "Luxe Living Furniture",
      email: "<EMAIL>",
      category: "Delivery Issue",
      subject: "Delay in furniture delivery",
      dateCreated: "Oct 10, 2025",
      status: "Pending",
    },
    {
      ticketId: "SP008",
      userType: "Furniture Store",
      name: "Modern Furnishings",
      email: "<EMAIL>",
      category: "Product Inquiry",
      subject: "Clarification on listing policy",
      dateCreated: "Oct 07, 2025",
      status: "Resolved",
    },
    {
      ticketId: "SP009",
      userType: "N/A",
      name: "Cozy Nest Interiors",
      email: "<EMAIL>",
      category: "App Performance",
      subject: "Slow loading time",
      dateCreated: "Oct 06, 2025",
      status: "Pending",
    },
    {
      ticketId: "SP010",
      userType: "Buyer",
      name: "James Walker",
      email: "<EMAIL>",
      category: "Refund Request",
      subject: "Refund not initiated",
      dateCreated: "Oct 05, 2025",
      status: "Pending",
    },
    {
      ticketId: "SP011",
      userType: "Furniture Store",
      name: "Emily Carter",
      email: "<EMAIL>",
      category: "Technical Support",
      subject: "Unable to upload catalog images",
      dateCreated: "Oct 02, 2025",
      status: "Resolved",
    },
  ]);

  const handleView = (ticketId: string) => {
    console.log("View ticket:", ticketId);
  };

  const handleStatusChange = (ticketId: string, newStatus: "Pending" | "Resolved") => {
    console.log("Change status for ticket:", ticketId, "to", newStatus);
  };

  return (
    <div className="users-list-container">
      {/* Header Section */}
      <HeaderSearch title="All Tickets" searchPlaceholder="Search tickets using name, email, type etc." showDownloadButton={false} />

      {/* Table Section */}
      <div className="table-scroll-wrapper">
        <div className="table-wrapper">
          <table className="users-table">
            <thead>
              <tr>
                <th>Ticket ID</th>
                <th>User Type</th>
                <th>Name/Store Name</th>
                <th>Email</th>
                <th>Category</th>
                <th>Subject</th>
                <th>Date Created</th>
                <th>Status</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {tickets.map((ticket) => (
                <tr key={ticket.ticketId}>
                  <td>{ticket.ticketId}</td>
                  <td>{ticket.userType}</td>
                  <td>{ticket.name}</td>
                  <td>{ticket.email}</td>
                  <td>{ticket.category}</td>
                  <td>
                    <p className="table-width">{ticket.subject}</p>
                  </td>
                  <td>{ticket.dateCreated}</td>
                  <td>
                    <Select
                      name={`status-${ticket.ticketId}`}
                      control={control}
                      options={[
                        { label: "Pending", value: "Pending" },
                        { label: "Resolved", value: "Resolved" },
                      ]}
                      className={`form-control status-select status-${ticket.status.toLowerCase()}`}
                      showPlaceholder={false}
                      onChange={(e) => handleStatusChange(ticket.ticketId, e.target.value as "Pending" | "Resolved")}
                    />
                  </td>
                  <td>
                    <div className="button-align justify-content-center">
                      <Button className="action-btn" onClick={() => handleView(ticket.ticketId)}>
                        <EyeIcon />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default SupportTickets;
